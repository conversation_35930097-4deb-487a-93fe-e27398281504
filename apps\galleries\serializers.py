from rest_framework import serializers
from .models import Gallery


class GallerySerializer(serializers.ModelSerializer):
    """Serializer for Gallery model"""
    picture_url = serializers.SerializerMethodField()
    
    class Meta:
        model = Gallery
        fields = ['id', 'title', 'picture', 'picture_url', 'created_on', 'updated_on']
        read_only_fields = ['id', 'created_on', 'updated_on', 'picture_url']
    
    def get_picture_url(self, obj):
        """Get full picture URL"""
        if obj.picture:
            return obj.picture.url
        return None


class GalleryCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating Gallery entries"""
    
    class Meta:
        model = Gallery
        fields = ['title', 'picture']
    
    def validate_picture(self, value):
        """Validate picture file"""
        if value:
            # Check file size (max 5MB)
            if value.size > 5 * 1024 * 1024:
                raise serializers.ValidationError("Image file too large. Maximum size is 5MB.")
            
            # Check file type
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
            if value.content_type not in allowed_types:
                raise serializers.ValidationError("Invalid image format. Allowed formats: JPEG, JPG, PNG, GIF.")
        
        return value
