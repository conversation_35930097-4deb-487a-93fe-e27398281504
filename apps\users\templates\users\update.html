﻿{% extends 'dashboard.html' %}
{% load static %}

{% block content %}
    <!-- =-=-=-=-=-=-= Blog & News =-=-=-=-=-=-= -->
    <section id="blog" class="container main">
        <h3 class="color-black">Profile Information Section</h3>
        <p>
            <a class="link" href="{% url 'user_profile' %}">Home</a> | 
            <a class="link" href="{% url 'user_profile' %}">Profile</a></h1> |
            view
        </p>
        <div class="">
            <!-- Row -->
            <div class="row">
            
                <div class="col-sm-12 col-xs-12 col-md-5 p-3">

                    <!-- sidebar -->
                    <div class="profile-sidebar">
                        <form action="{% url 'update_profile' user.id %}" method="post" enctype="multipart/form-data">
                            <div class="form-group">
                                {% if user.profile_picture %}
                                    <img id="img-upload" class="img-responsive" src="{{ user.profile_picture.url }}" alt="profile image"/>
                                {% else %}
                                    <img id="img-upload" class="img-responsive" src="{% static './media/images/dummy_qr.png' %}" alt="profile image"/>
                                {% endif %}
                            </div>
                        </form>
                    </div>
                    <!-- sidebar end -->
                </div>
            
                <div class="col-sm-12 col-xs-12 col-md-6">

                    <div class="row">
                        <div class="profile-section margin-bottom-20">
                            <div class="profile-tabs">
                                <ul class="nav nav-justified nav-tabs">
                                    <li class="active"><a href="#profile" data-toggle="tab">Profile Detail</a></li>
                                    <li><a href="#edit" data-toggle="tab">Edit Profile</a></li>
                                </ul>
                                <div class="tab-content">
                                    <div class="profile-edit tab-pane fade in active" id="profile">
                                        <h2 class="heading-md">Manage your Name, ID and Email Addresses.</h2>
                                        <p>Below are the name and email addresses on file for your account.</p>
                                        <br>
                                        <dl class="dl-horizontal">
                                            {% if business_details %}
                                                <dt><strong>Business Name </strong></dt>
                                                <dd>{{ user.get_business_name }}</dd>
                                                <hr>

                                                <dt><strong>Business Address </strong></dt>
                                                <dd>{{ business_details.company_address|capfirst }}</dd>
                                                <hr>
                                            {% endif %}

                                            <dt><strong>Your name </strong></dt>
                                            <dd>{{ user.get_full_name|capfirst }}</dd>
                                            <hr>

                                            <dt><strong>Your ID </strong></dt>
                                            <dd>{{ user.profile_code }}</dd>
                                            <hr>

                                            <dt><strong>Email Address </strong></dt>
                                            <dd>{{ user.email }}</dd>
                                            <hr>

                                            <dt><strong>Phone Number </strong></dt>
                                            <dd>{{ user.phone_no }}</dd>
                                            <hr>

                                            <dt><strong>Office Number </strong></dt>
                                            <dd>{{ user.phone_no }}</dd>
                                            <hr>

                                            <dt><strong>Address </strong></dt>
                                            <dd>{{ user.city|capfirst }} {{ user.state|capfirst }}</dd>
                                            <hr>
                                        </dl>
                                    </div>

                                    <div class="profile-edit tab-pane fade" id="edit">
                                        <h2 class="heading-md">Manage your Security Settings</h2>
                                        <p>Change your password.</p>
                                        <br>
                                        <form method="post" enctype="multipart/form-data">
                                            
                                            {% csrf_token %}
                                            
                                            <div class="">
                                                <label for="{{form.first_name.label}}">{{form.first_name.label}}</label>
                                                {{form.first_name}}
                                            </div>

                                            <div class="mt-3">
                                                <label for="{{form.last_name.label}}">{{form.last_name.label}}</label>
                                                {{form.last_name}}
                                            </div>

                                            <div class="">
                                                <label for="{{form.email.label}}">{{form.email.label}}</label>
                                                {{form.email}}
                                            </div>

                                            <div class="">
                                                <label for="{{form.phone_no.label}}">{{form.phone_no.label}}</label>
                                                {{form.phone_no}}
                                            </div>

                                            <div class="">
                                                <label for="{{form.address.label}}">{{form.address.label}}</label>
                                                {{form.address}}
                                            </div>

                                            <div class="">
                                                <label for="{{form.city.label}}">{{form.city.label}}</label>
                                                {{form.city}}
                                            </div>

                                            <div class="">
                                                <label for="{{form.state.label}}">{{form.state.label}}</label>
                                                {{form.state}}
                                            </div>
                                            
                                            <div class="">
                                                <label for="{{form.profile_picture.label}}">{{form.profile_picture.label}}</label>
                                                {{form.profile_picture}}
                                            </div>

                                            <div class="row">
                                                <div class="col-md-8 col-sm-8 col-xs-12">
                                                    <div class="checkbox checkbox-primary">
                                                        <input type="checkbox" checked="" id="checkbox2">
                                                        <label for="checkbox2">
                                                            I read <a href="#">Terms and Conditions</a>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="col-md-4 col-sm-4 col-xs-12 text-right mt-0">
                                                        <button type="submit" class="btn btn-primary btn-sm">Update My Info</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Row End -->
        </div>
        <!-- end container -->
    </section>
    <!-- =-=-=-=-=-=-= Blog & News end =-=-=-=-=-=-= -->
{% endblock content %}