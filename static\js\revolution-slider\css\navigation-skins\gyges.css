/*-----------------------------------------------------------------------------

- Revolution Slider 5.0 Navigatin Skin Style  -

 GYGES SKIN

author:  ThemePunch
email:      <EMAIL>
website:    http://www.themepunch.com
-----------------------------------------------------------------------------*/
/* ARROWS */


/* BULLETS */
.gyges.tp-bullets {
}
.gyges.tp-bullets:before {
	content:" ";
	position:absolute;
	width:100%;
	height:100%;
	background: #777777; /* Old browsers */
    background: -moz-linear-gradient(top,  #777777 0%, #666666 100%); 
    background: -webkit-gradient(linear, left top, left bottom, 
    color-stop(0%,#777777), color-stop(100%,#666666)); 
    background: -webkit-linear-gradient(top,  #777777 0%,#666666 100%); 
    background: -o-linear-gradient(top,  #777777 0%,#666666 100%); 
    background: -ms-linear-gradient(top,  #777777 0%,#666666 100%); 
    background: linear-gradient(to bottom,  #777777 0%,#666666 100%); 
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#777777", 
    endColorstr="#666666",GradientType=0 ); 
	padding:10px;
	margin-left:-10px;margin-top:-10px;
	box-sizing:content-box;
  border-radius:10px;
}
.gyges .tp-bullet {
	width:12px;
	height:12px;
	position:absolute;
	background:#333;
	border:3px solid #444;
	border-radius:50%;
	cursor: pointer;
	box-sizing:content-box;
}
.gyges .tp-bullet:hover,
.gyges .tp-bullet.selected {
	background: #ffffff; /* Old browsers */
    background: -moz-linear-gradient(top,  #ffffff 0%, #e1e1e1 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, 
    color-stop(0%,#ffffff), color-stop(100%,#e1e1e1)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #ffffff 0%,#e1e1e1 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #ffffff 0%,#e1e1e1 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #ffffff 0%,#e1e1e1 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #ffffff 0%,#e1e1e1 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#ffffff", 
    endColorstr="#e1e1e1",GradientType=0 ); /* IE6-9 */

}
.gyges .tp-bullet-image {
}
.gyges .tp-bullet-title {
}
	

/* THUMBS */
.gyges .tp-thumb { 
      opacity:1
  }
.gyges .tp-thumb-img-wrap {
  padding:3px;
    background:#000;
  background-color:rgba(0,0,0,0.25);
  display:inline-block;

  width:100%;
  height:100%;
  position:relative;
  margin:0px;
  box-sizing:border-box;
    transition:all 0.3s;
    -webkit-transition:all 0.3s;
}
.gyges .tp-thumb-image {
   padding:3px; 
   display:block;
   box-sizing:border-box;
   position:relative;
    -webkit-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);
  -moz-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);
  box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);
 }  
.gyges .tp-thumb-title { 
     position:absolute; 
     bottom:100%; 
     display:inline-block;
     left:50%;
     background:rgba(255,255,255,0.8);
     padding:10px 30px;
     border-radius:4px;
	 -webkit-border-radius:4px;
     margin-bottom:20px;
     opacity:0;
      transition:all 0.3s;
    -webkit-transition:all 0.3s;
    transform: translateZ(0.001px) translateX(-50%) translateY(14px);
    transform-origin:50% 100%;
    -webkit-transform: translateZ(0.001px) translateX(-50%) translateY(14px);
    -webkit-transform-origin:50% 100%;
    white-space:nowrap;
 }
.gyges .tp-thumb:hover .tp-thumb-title {
  	 transform:rotateX(0deg) translateX(-50%);
    -webkit-transform:rotateX(0deg) translateX(-50%);
    opacity:1;
}

.gyges .tp-thumb:hover .tp-thumb-img-wrap,
 .gyges .tp-thumb.selected .tp-thumb-img-wrap {

  background: rgba(255,255,255,1);
  background: -moz-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);
  background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(255,255,255,1)), color-stop(100%, rgba(119,119,119,1)));
  background: -webkit-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);
  background: -o-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);
  background: -ms-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);
  background: linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#ffffff", endColorstr="#777777", GradientType=0 );
 }
.gyges .tp-thumb-title:after {
        content:" ";
        position:absolute;
        left:50%;
        margin-left:-8px;
        width: 0;
		height: 0;
		border-style: solid;
		border-width: 8px 8px 0 8px;
		border-color: rgba(255,255,255,0.8) transparent transparent transparent;
        bottom:-8px;
   }


/* TABS */
.gyges .tp-tab { 
  opacity:1;      
  padding:10px;
  box-sizing:border-box;
  font-family: "Roboto", sans-serif;
  border-bottom: 1px solid rgba(255,255,255,0.15);
 }
.gyges .tp-tab-image 
{ 
  width:60px;
  height:60px; max-height:100%; max-width:100%;
  position:relative;
  display:inline-block;
  float:left;

}
.gyges .tp-tab-content 
{
    background:rgba(0,0,0,0); 
    position:relative;
    padding:15px 15px 15px 85px;
 left:0px;
  overflow:hidden;
 margin-top:-15px;
    box-sizing:border-box;
    color:#333;
    display: inline-block;
    width:100%;
    height:100%;
 position:absolute; }
.gyges .tp-tab-date
  {
  display:block;
  color: rgba(255,255,255,0.25);
  font-weight:500;
  font-size:12px;
  margin-bottom:0px;
  }
.gyges .tp-tab-title 
{
    display:block;  
    text-align:left;
    color:#fff;
    font-size:14px;
    font-weight:500;
    text-transform:none;
    line-height:17px;
}
.gyges .tp-tab:hover,
.gyges .tp-tab.selected {
  background:rgba(0,0,0,0.5); 
}

.gyges .tp-tab-mask {
}

/* MEDIA QUERIES */
@media only screen and (max-width: 960px) {

}
@media only screen and (max-width: 768px) {

}

