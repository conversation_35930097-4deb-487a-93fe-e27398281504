from django.urls import path
from .import views


urlpatterns = [
    path('order-list', views.company_order_list, name="company_order_list"),
    path('order-detail/<str:id>', views.company_order_detail, name="company_order_detail"),
    path('initiate-payment', views.payment_view, name="make_order_payment"),
    path('verify-payment', views.verify_payment, name="verify_order_payment"),
    path('track-local-order', views.TrackLocalOrderDeliveryView.as_view(), name="track_local_order"),
    path('local-order-result', views.LocalOrderDeliveryResultView.as_view(), name="logistics_tracking_result"),
    path('local-order-delivery-list', views.dispatcher_assigned_delivery_list, name="local_order_delivery_list"),
    path('update-local-order-delivery/<str:id>', views.dispatcher_update_order_delivery, name="local_order_delivery_update")
]
