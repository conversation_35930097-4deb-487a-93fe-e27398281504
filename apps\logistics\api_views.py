from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db import transaction
from .models import Logistic, Item
from apps.company.models import Company
from .serializers import (
    LogisticCreateSerializer,
    LogisticListSerializer,
    LogisticDetailSerializer,
    LogisticTrackingSerializer,
    LogisticStatusUpdateSerializer
)


class LogisticCreateAPIView(generics.CreateAPIView):
    """API View to create new logistic order (Company users only)"""
    serializer_class = LogisticCreateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_context(self):
        """Add request to serializer context"""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context
    
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """Create new logistic order"""
        # Check if user has a company
        try:
            company = Company.objects.get(user=request.user)
        except Company.DoesNotExist:
            return Response({
                'success': False,
                'message': 'You must register a company first to create logistics orders'
            }, status=status.HTTP_403_FORBIDDEN)
        
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            logistic_order = serializer.save()
            
            return Response({
                'success': True,
                'message': 'Logistics order created successfully',
                'data': {
                    'id': str(logistic_order.id),
                    'tracking_no': logistic_order.tracking_no,
                    'delivery_fee': logistic_order.delivery_fee,
                    'payment_status': logistic_order.payment_status,
                    'delivery_status': logistic_order.delivery_status
                }
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'message': 'Logistics order creation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class LogisticOrdersListAPIView(generics.ListAPIView):
    """API View to list company's logistic orders"""
    serializer_class = LogisticListSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get logistics orders for current user's company"""
        try:
            company = Company.objects.get(user=self.request.user)
            return Logistic.objects.select_related('company', 'sender', 'dispatcher').filter(
                company=company
            ).order_by('-created_on')
        except Company.DoesNotExist:
            return Logistic.objects.none()
    
    def list(self, request, *args, **kwargs):
        """List company's logistic orders with pagination"""
        queryset = self.get_queryset()
        
        if not queryset.exists():
            return Response({
                'success': False,
                'message': 'No company found for this user'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Get pagination parameters
        page = request.GET.get('page', 1)
        limit = min(int(request.GET.get('limit', 20)), 100)  # Max 100 items per page
        
        # Paginate results
        paginator = Paginator(queryset, limit)
        page_obj = paginator.get_page(page)
        
        # Serialize data
        serializer = self.get_serializer(page_obj.object_list, many=True)
        
        return Response({
            'success': True,
            'data': {
                'count': paginator.count,
                'next': page_obj.next_page_number() if page_obj.has_next() else None,
                'previous': page_obj.previous_page_number() if page_obj.has_previous() else None,
                'results': serializer.data
            }
        }, status=status.HTTP_200_OK)


class LogisticDetailAPIView(generics.RetrieveAPIView):
    """API View to get detailed logistic order information"""
    serializer_class = LogisticDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'
    
    def get_queryset(self):
        """Get logistics orders for current user's company"""
        try:
            company = Company.objects.get(user=self.request.user)
            return Logistic.objects.select_related('company', 'sender', 'dispatcher').filter(
                company=company
            )
        except Company.DoesNotExist:
            return Logistic.objects.none()
    
    def retrieve(self, request, *args, **kwargs):
        """Get detailed logistic order information"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            
            return Response({
                'success': True,
                'data': serializer.data
            }, status=status.HTTP_200_OK)
        except:
            return Response({
                'success': False,
                'message': 'Logistics order not found or access denied'
            }, status=status.HTTP_404_NOT_FOUND)


class DispatcherAssignmentsAPIView(generics.ListAPIView):
    """API View for dispatchers to see their assigned orders"""
    serializer_class = LogisticListSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get logistics orders assigned to current dispatcher"""
        user = self.request.user
        if user.user_type != 'dispatcher':
            return Logistic.objects.none()
        
        return Logistic.objects.select_related('company', 'sender').filter(
            dispatcher=user
        ).order_by('-created_on')
    
    def list(self, request, *args, **kwargs):
        """List dispatcher's assigned orders"""
        user = request.user
        
        if user.user_type != 'dispatcher':
            return Response({
                'success': False,
                'message': 'Access denied. Only dispatchers can view assignments'
            }, status=status.HTTP_403_FORBIDDEN)
        
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class LogisticStatusUpdateAPIView(generics.UpdateAPIView):
    """API View for dispatchers to update delivery status"""
    serializer_class = LogisticStatusUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'
    
    def get_queryset(self):
        """Get logistics orders assigned to current dispatcher"""
        user = self.request.user
        if user.user_type != 'dispatcher':
            return Logistic.objects.none()
        
        return Logistic.objects.filter(dispatcher=user)
    
    def update(self, request, *args, **kwargs):
        """Update delivery status"""
        user = request.user
        
        if user.user_type != 'dispatcher':
            return Response({
                'success': False,
                'message': 'Access denied. Only dispatchers can update delivery status'
            }, status=status.HTTP_403_FORBIDDEN)
        
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            
            if serializer.is_valid():
                serializer.save()
                
                return Response({
                    'success': True,
                    'message': 'Delivery status updated successfully'
                }, status=status.HTTP_200_OK)
            
            return Response({
                'success': False,
                'message': 'Status update failed',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
            
        except:
            return Response({
                'success': False,
                'message': 'Logistics order not found or access denied'
            }, status=status.HTTP_404_NOT_FOUND)


class LogisticTrackingAPIView(APIView):
    """API View for public logistic tracking"""
    permission_classes = [permissions.AllowAny]  # Public endpoint
    
    def get(self, request, tracking_number):
        """Track logistic order by tracking number"""
        try:
            logistic_order = Logistic.objects.select_related('company', 'sender').get(
                tracking_no=tracking_number
            )
            
            serializer = LogisticTrackingSerializer(logistic_order)
            
            return Response({
                'success': True,
                'data': {
                    'tracking_no': logistic_order.tracking_no,
                    'type': 'logistics',
                    'status': logistic_order.delivery_status,
                    'company': serializer.data['company'],
                    'sender': serializer.data['sender'],
                    'destination': {
                        'name': logistic_order.receiver_name,
                        'address': serializer.data['receiver_address_display']
                    },
                    'weight': serializer.data['weight_display'],
                    'shipped_at': logistic_order.shipped_at,
                    'estimated_delivery': None,  # You can add logic for estimated delivery
                    'tracking_history': [
                        {
                            'status': 'Order Created',
                            'timestamp': logistic_order.created_on,
                            'location': serializer.data['sender']['address'] if serializer.data['sender'] else 'Origin'
                        },
                        {
                            'status': logistic_order.delivery_status,
                            'timestamp': logistic_order.shipped_at or logistic_order.updated_on,
                            'location': 'In Transit' if logistic_order.delivery_status != 'Pending' else 'Origin'
                        }
                    ]
                }
            }, status=status.HTTP_200_OK)
            
        except Logistic.DoesNotExist:
            return Response({
                'success': False,
                'message': 'No matching logistics order found for this tracking number'
            }, status=status.HTTP_404_NOT_FOUND)
