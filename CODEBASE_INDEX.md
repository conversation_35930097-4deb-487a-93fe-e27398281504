# MOG Dynamics - Codebase Index

## Project Overview
**MOG Dynamics** is a comprehensive Django-based logistics and shipping management platform that handles import/export operations, local logistics, and business management. The platform provides tracking, payment processing, user management, and automated notifications.

**Live URL**: https://mogdynamics.up.railway.app/

## Architecture & Technology Stack

### Core Framework
- **Django 4.2.14** - Main web framework
- **Python 3.10.4** - Programming language
- **PostgreSQL** - Primary database
- **Redis** - Caching and message broker
- **Celery** - Background task processing
- **Gunicorn** - WSGI HTTP Server

### Key Third-Party Integrations
- **Cloudinary** - Media storage and image management
- **Paystack** - Payment processing
- **Django Money** - Multi-currency support
- **Crispy Forms** - Form rendering
- **Jazzmin** - Enhanced Django admin interface
- **Phone Number Field** - International phone number handling

## Project Structure

```
mog_dynamics/
├── core/                    # Django project core
│   ├── settings/           # Environment-specific settings
│   ├── celery.py          # Celery configuration
│   ├── tasks.py           # Background tasks
│   ├── urls.py            # Main URL routing
│   └── views.py           # Core views
├── apps/                   # Django applications
│   ├── users/             # User management & authentication
│   ├── company/           # Business registration & management
│   ├── exports/           # Export shipping operations
│   ├── imports/           # Import shipping operations
│   ├── logistics/         # Local delivery services
│   ├── addresses/         # Address management
│   ├── pricing/           # Pricing information
│   ├── galleries/         # Image galleries
│   └── common/            # Shared utilities & helpers
├── templates/             # HTML templates
├── static/               # Static files (CSS, JS, images)
├── docker/               # Docker configuration
├── requirements/         # Python dependencies
└── fixtures/             # Database fixtures
```

## Django Applications

### 1. Users App (`apps/users/`)
**Purpose**: Custom user management, authentication, and user profiles

**Key Models**:
- `CustomUser` - Extended user model with profile information
  - Email-based authentication (no username)
  - Profile pictures via Cloudinary
  - Document verification system
  - User types: regular, dispatcher, small business
  - Geographic information (country, state, city)

**Key Features**:
- Custom user manager
- Email verification system
- Profile management
- Document upload for verification
- User type-based permissions

### 2. Company App (`apps/company/`)
**Purpose**: Business registration and company profile management

**Key Models**:
- `Company` - Business information storage
  - RC number validation (7 characters)
  - CAC certificate upload
  - Business address management
  - User-company relationship

**Key Features**:
- Business registration
- Document verification
- Company profile management

### 3. Exports App (`apps/exports/`)
**Purpose**: International export shipping operations

**Key Models**:
- `Export` - Export shipment records
  - Customer and address relationships
  - Weight and dimensional data
  - Payment tracking
  - Shipping status management
  - Auto-generated tracking numbers (EXP prefix)
- `Item` - Individual items in export shipments
  - Multi-currency pricing (USD default)
  - Quantity and weight tracking

**Key Features**:
- Export booking system
- Payment integration with Paystack
- Tracking number generation
- Status updates and notifications
- Item management

### 4. Imports App (`apps/imports/`)
**Purpose**: International import shipping operations

**Key Models**:
- `Import` - Import shipment records
  - Similar structure to Export model
  - Auto-generated tracking numbers (IMP prefix)
  - Payment and status tracking
- `Item` - Items in import shipments
  - USD currency for international pricing

**Key Features**:
- Import booking system
- Payment processing
- Shipment tracking
- Status management
- Email notifications

### 5. Logistics App (`apps/logistics/`)
**Purpose**: Local delivery and logistics services

**Key Models**:
- `Logistic` - Local delivery orders
  - Company-based orders
  - Dispatcher assignment
  - Local currency (NGN) pricing
  - Auto-generated tracking numbers (LCL prefix)
- `Item` - Items in local deliveries
  - NGN currency for local pricing

**Key Features**:
- Local delivery management
- Dispatcher assignment system
- Company order tracking
- Payment processing
- Delivery status updates

### 6. Addresses App (`apps/addresses/`)
**Purpose**: Address management for shipping

**Key Models**:
- `Address` - Standardized address storage
  - International phone number support
  - Country-specific validation
  - Reusable address records

### 7. Common App (`apps/common/`)
**Purpose**: Shared utilities, validators, and helper functions

**Key Components**:
- `choices.py` - Dropdown choices for forms
- `custom_validators.py` - File validation logic
- `decorators.py` - Authentication decorators
- `generator.py` - Tracking number and reference generators
- `mail_helper.py` - Email sending utilities
- `models.py` - Base model classes (TimeStampedModel)
- `paystack.py` - Payment processing helpers

### 8. Pricing & Galleries Apps
- **Pricing**: Static pricing information display
- **Galleries**: Image gallery management with Cloudinary integration

## Core Features

### Authentication & Authorization
- Custom user model with email-based login
- Role-based access control (regular, dispatcher, business)
- Document verification system
- Profile management with image uploads

### Payment Processing
- Paystack integration for all payment types
- Multi-currency support (USD for international, NGN for local)
- Payment verification and status tracking
- Automated reference number generation

### Shipping & Logistics
- Three shipping types: Export, Import, Local Logistics
- Automated tracking number generation
- Weight and dimensional calculations
- Status tracking throughout shipping lifecycle
- Dispatcher assignment for local deliveries

### Notification System
- Celery-based background email processing
- Multiple email templates for different scenarios
- Account activation, payment confirmation, shipping updates
- Contact form processing

### File Management
- Cloudinary integration for all file uploads
- Automatic file validation and size limits
- Organized folder structure for different file types
- Image optimization and delivery

## Database Design

### Key Relationships
- Users can have multiple Companies
- Companies can create Logistics orders
- Users can create Import/Export shipments
- All shipments link to Address records
- Items belong to specific shipments
- Payments are tracked per shipment

### Common Patterns
- All models extend `TimeStampedModel` (UUID primary keys, created/updated timestamps)
- Soft deletes using SET_NULL for important relationships
- Tracking numbers generated automatically
- Payment status and shipping status enums

## Background Processing

### Celery Tasks (`core/tasks.py`)
- Account activation emails
- Password reset emails
- Contact form notifications
- Export/Import notifications
- Order booking confirmations

### Redis Configuration
- Message broker for Celery
- Caching layer
- Session storage

## Deployment & Infrastructure

### Docker Support
- Development and production Docker configurations
- Multi-service setup (app, Redis, Celery worker)
- Environment-specific Dockerfiles

### Railway Deployment
- Procfile for Railway platform
- Automatic migrations and static file collection
- Database fixtures loading
- Celery worker process

### Environment Configuration
- Separate settings for dev/prod
- Environment variable management
- Database configuration per environment
- CORS and security settings

## Security Features
- CSRF protection
- File upload validation
- User authentication decorators
- Secure payment processing
- Environment-based secret management

## API Endpoints Structure

### Main Routes (`core/urls.py`)
- `/` - Home page
- `/admin/` - Django admin
- `/users/` - User management
- `/company/` - Company operations
- `/exports/` - Export services
- `/imports/` - Import services
- `/logistics/` - Local logistics
- `/pricing/` - Pricing information
- `/galleries/` - Image galleries

### Common URL Patterns
- List views: `/{service}-records`
- Detail views: `/{service}-details/<id>`
- Payment: `/initiate-payment`, `/verify-payment`
- Tracking: `/track-{service}`

## Development Setup
1. Python 3.10.4 environment
2. PostgreSQL database
3. Redis server
4. Environment variables configuration
5. Docker Compose for local development

## Key Business Logic
- Automatic tracking number generation
- Multi-currency pricing calculations
- Payment verification workflows
- Status update notifications
- Dispatcher assignment algorithms
- Document verification processes

This codebase represents a full-featured logistics platform with comprehensive shipping management, payment processing, and user management capabilities.

---

## Flutter Mobile App Integration

**Yes, this Django application can absolutely be used as an API backend for a Flutter mobile app!**

### 📱 API Documentation
For detailed API endpoints and Flutter integration guide, see: **[API_DOCUMENTATION.md](./API_DOCUMENTATION.md)**

### 🚀 Quick API Overview
The current web-based views can be converted to REST API endpoints that return JSON responses. The API will support:

- **Authentication**: JWT-based user authentication
- **User Management**: Profile management and dashboard
- **Shipment Operations**: Export, Import, and Local Logistics
- **Payment Processing**: Paystack integration
- **Real-time Tracking**: Public shipment tracking
- **File Uploads**: Document and image handling via Cloudinary

### 🛠 Implementation Requirements
To convert to API backend, you'll need to:
1. Install Django REST Framework
2. Create serializers for existing models
3. Convert views to API endpoints
4. Add JWT authentication
5. Configure CORS for mobile access

The complete implementation guide and all endpoint specifications are documented in the separate API documentation file.
