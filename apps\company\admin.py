from django.contrib import admin
from apps.company.models import Company


@admin.register(Company)
class AdminCompany(admin.ModelAdmin):
    list_display = ('user', 'business_name', 'rc_number', 'created_on', 'updated_on')
    readonly_fields = ('id', 'created_on', 'updated_on')
    search_fields = (
        'business_name',
        'rc_number',
        'user__first_name',
        'user__last_name',
        'user__profile_code',
        'created_on',
        'updated_on'
    )
