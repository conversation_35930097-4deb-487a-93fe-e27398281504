/*-----------------------------------------------------------------------------

- Revolution Slider 5.0 Navigatin Skin Style  -

 ZEUS SKIN

author:  ThemePunch
email:      <EMAIL>
website:    http://www.themepunch.com
-----------------------------------------------------------------------------*/
/* ARROWS */
.zeus.tparrows {
  cursor:pointer;
  min-width:70px;
  min-height:70px;
  position:absolute;
  display:block;
  z-index:100;
  border-radius:35px;   
  overflow:hidden;
  background:rgba(0,0,0,0.10);
}

.zeus.tparrows:before {
  font-family: "revicons";
  font-size:20px;
  color:#fff;
  display:block;
  line-height: 70px;
  text-align: center;    
  z-index:2;
  position:relative;
}
.zeus.tparrows.tp-leftarrow:before {
  content: "\e824";
}
.zeus.tparrows.tp-rightarrow:before {
  content: "\e825";
}

.zeus .tp-title-wrap {
  background:#000;
  background:rgba(0,0,0,0.5);
  width:100%;
  height:100%;
  top:0px;
  left:0px;
  position:absolute;
  opacity:0;
  transform:scale(0);
  -webkit-transform:scale(0);
   transition: all 0.3s;
  -webkit-transition:all 0.3s;
  -moz-transition:all 0.3s;
   border-radius:50%;
 }
.zeus .tp-arr-imgholder {
  width:100%;
  height:100%;
  position:absolute;
  top:0px;
  left:0px;
  background-position:center center;
  background-size:cover;
  border-radius:50%;
  transform:translateX(-100%);
  -webkit-transform:translateX(-100%);
   transition: all 0.3s;
  -webkit-transition:all 0.3s;
  -moz-transition:all 0.3s;

 }
.zeus.tp-rightarrow .tp-arr-imgholder {
    transform:translateX(100%);
  -webkit-transform:translateX(100%);
      }
.zeus.tparrows:hover .tp-arr-imgholder {
  transform:translateX(0);
  -webkit-transform:translateX(0);
  opacity:1;
}
      
.zeus.tparrows:hover .tp-title-wrap {
  transform:scale(1);
  -webkit-transform:scale(1);
  opacity:1;
}
 

/* BULLETS */
.zeus .tp-bullet {
     box-sizing:content-box; -webkit-box-sizing:content-box; border-radius:50%;
      background-color: rgba(0, 0, 0, 0);
      -webkit-transition: opacity 0.3s ease;
      transition: opacity 0.3s ease;
	  width:13px;height:13px;
	  border:2px solid #fff;
 }
.zeus .tp-bullet:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  border-radius: 50%;
  background-color: #FFF;
  -webkit-transform: scale(0);
  transform: scale(0);
  -webkit-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
}
.zeus .tp-bullet:hover:after,
.zeus .tp-bullet.selected:after{
    -webkit-transform: scale(1.2);
  transform: scale(1.2);
}
  
 .zeus .tp-bullet-image,
 .zeus .tp-bullet-imageoverlay{
        width:135px;
        height:60px;
        position:absolute;
        background:#000;
        background:rgba(0,0,0,0.5);
        bottom:25px;
        left:50%;
        margin-left:-65px;
        box-sizing:border-box;
        background-size:cover;
        background-position:center center;
        visibility:hidden;
        opacity:0;
         -webkit-backface-visibility: hidden; 
      	backface-visibility: hidden;
        -webkit-transform-origin: 50% 50%;
		transform-origin: 50% 50%;
  		-webkit-transition: all 0.3s ease;
  		transition: all 0.3s ease;
        border-radius:4px;

}
          

.zeus .tp-bullet-title,
.zeus .tp-bullet-imageoverlay {
        z-index:2;
        -webkit-transition: all 0.5s ease;
	  	transition: all 0.5s ease;
}     
.zeus .tp-bullet-title { 
        color:#fff;
        text-align:center;
        line-height:15px;
        font-size:13px;
        font-weight:600;  
        z-index:3;
         visibility:hidden;
        opacity:0;
         -webkit-backface-visibility: hidden; 
      	backface-visibility: hidden;
        -webkit-transform-origin: 50% 50%;
		transform-origin: 50% 50%;
  		-webkit-transition: all 0.3s ease;
  		transition: all 0.3s ease;
        position:absolute;
        bottom:45px;
        width:135px;
    	vertical-align:middle;
        left:-57px;
}
      
.zeus .tp-bullet:hover .tp-bullet-title,
.zeus .tp-bullet:hover .tp-bullet-image,
.zeus .tp-bullet:hover .tp-bullet-imageoverlay{
      opacity:1;
      visibility:visible;
	  -webkit-transform:translateY(0px);
      transform:translateY(0px);         
    }

/* THUMBS */
.zeus .tp-thumb {
opacity:1
}

.zeus .tp-thumb-over {
  background:#000;
  background:rgba(0,0,0,0.25);
  width:100%;
  height:100%;
  position:absolute;
  top:0px;
  left:0px;
  z-index:1;
  -webkit-transition:all 0.3s;
  transition:all 0.3s;
}

.zeus .tp-thumb-more:before {
  font-family: "revicons";
  font-size:12px;
  color:#aaa;
  color:rgba(255,255,255,0.75);
  display:block;
  line-height: 12px;
  text-align: left;    
  z-index:2;
  position:absolute;
  top:20px;
  right:20px;
  z-index:2;
}
.zeus .tp-thumb-more:before {
  content: "\e825";
}

.zeus .tp-thumb-title {
  font-family:"Raleway";
  letter-spacing:1px;
  font-size:12px;
  color:#fff;
  display:block;
  line-height: 15px;
  text-align: left;    
  z-index:2;
  position:absolute;
  top:0px;
  left:0px;
  z-index:2;
  padding:20px 35px 20px 20px;
  width:100%;
  height:100%;
  box-sizing:border-box;
  transition:all 0.3s;
  -webkit-transition:all 0.3s;
  font-weight:500;
}

.zeus .tp-thumb.selected .tp-thumb-more:before,
.zeus .tp-thumb:hover .tp-thumb-more:before {
 color:#aaa;

}

.zeus .tp-thumb.selected .tp-thumb-over,
.zeus .tp-thumb:hover .tp-thumb-over {
 background:#000;
}
.zeus .tp-thumb.selected .tp-thumb-title,
.zeus .tp-thumb:hover .tp-thumb-title {
  color:#fff;

}


/* TABS */
.zeus .tp-tab { 
  opacity:1;      
  box-sizing:border-box;
}

.zeus .tp-tab-title { 
display: block;
text-align: center;
background: rgba(0,0,0,0.25);
font-family: "Roboto Slab", serif; 
font-weight: 700; 
font-size: 13px; 
line-height: 13px;
color: #fff; 
padding: 9px 10px; }

.zeus .tp-tab:hover .tp-tab-title,
.zeus .tp-tab.selected .tp-tab-title {
 color: #000;
  background:rgba(255,255,255,1); 
}

