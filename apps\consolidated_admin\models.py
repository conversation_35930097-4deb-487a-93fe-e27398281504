from django.db import models
from django.urls import reverse
from apps.common import choices
from apps.users.models import CustomUser
from apps.addresses.models import Address
from djmoney.models.fields import MoneyField
from apps.common.models import TimeStampedModel
from phonenumber_field.modelfields import PhoneNumberField


class ShipmentType(models.TextChoices):
    IMPORT = 'import', 'Import'
    EXPORT = 'export', 'Export'


class ConsolidatedShipment(TimeStampedModel):
    """
    Unified model for both import and export shipments
    """
    shipment_type = models.CharField(
        max_length=10,
        choices=ShipmentType.choices,
        help_text="Type of shipment - Import or Export"
    )
    customer = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    sender = models.ForeignKey(Address, on_delete=models.SET_NULL, null=True, related_name="consolidated_sender")
    destination = models.ForeignKey(
        Address,
        on_delete=models.SET_NULL,
        null=True,
        related_name="consolidated_destination"
    )
    phone_no = PhoneNumberField(blank=True, max_length=14)
    weight = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    weight_unit = models.CharField(choices=choices.weight_unit, default="Kg")
    length = models.FloatField(default=0.00, blank=True)
    breadth = models.FloatField(default=0.00, blank=True)
    height = models.FloatField(default=0.00, blank=True)
    volumetric_weight = models.FloatField(default=0.00, blank=True)
    volumetric_weight_unit = models.CharField(choices=choices.weight_unit, default="Kg")
    shipping_fee = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    tracking_no = models.CharField(max_length=50, db_index=True, unique=True)
    payment_status = models.CharField(choices=choices.shipping_payment_choices, default="Unpaid", max_length=100)
    payment_reference = models.CharField(max_length=100, blank=True, null=True)
    shipped_at = models.DateTimeField(null=True, blank=True)
    shipping_status = models.CharField(default="Pending", max_length=255, choices=choices.shipping_status_choices)
    tracking_url = models.URLField(blank=True, help_text="External tracking URL (mainly for exports)")
    attention = models.TextField(blank=True)
    email_sent = models.BooleanField(default=False)
    
    # Reference to original import/export record
    import_reference = models.ForeignKey(
        'imports.Import', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        help_text="Reference to original import record"
    )
    export_reference = models.ForeignKey(
        'exports.Export', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        help_text="Reference to original export record"
    )

    class Meta:
        verbose_name = "Consolidated Shipment"
        verbose_name_plural = "Consolidated Shipments"
        ordering = ("-created_on",)
        indexes = [
            models.Index(fields=['shipment_type', 'shipping_status']),
            models.Index(fields=['customer', 'shipment_type']),
            models.Index(fields=['tracking_no']),
        ]

    def __str__(self):
        return f"{self.get_shipment_type_display()} - {self.customer} - {self.tracking_no}"

    def get_absolute_url(self):
        return reverse("consolidated_shipment_detail", kwargs={"id": self.id})

    @property
    def get_weight(self):
        return f"{self.weight} {self.weight_unit}" if self.weight else "N/A"

    @property
    def get_volumetric_weight(self):
        return f"{self.volumetric_weight} {self.volumetric_weight_unit.capitalize()}"

    @property
    def is_import(self):
        return self.shipment_type == ShipmentType.IMPORT

    @property
    def is_export(self):
        return self.shipment_type == ShipmentType.EXPORT

    def save(self, *args, **kwargs):
        # Calculate volumetric weight
        if self.length and self.breadth and self.height:
            self.volumetric_weight = round((self.length * self.breadth * self.height) / 5000, 2)
        
        # Set tracking number if not provided
        if not self.tracking_no:
            if self.shipment_type == ShipmentType.IMPORT:
                from apps.common.generator import import_tracking_no
                self.tracking_no = import_tracking_no()
            else:
                from apps.common.generator import export_tracking_no
                self.tracking_no = export_tracking_no()
        
        return super().save(*args, **kwargs)


class ConsolidatedItem(TimeStampedModel):
    """
    Unified model for both import and export items
    """
    shipment = models.ForeignKey(ConsolidatedShipment, on_delete=models.CASCADE, related_name='items')
    name = models.CharField(max_length=255)
    quantity = models.IntegerField(default=1)
    price = MoneyField(max_digits=10, decimal_places=2, default_currency='USD')
    total_price = MoneyField(max_digits=10, decimal_places=2, default_currency='USD', blank=True, null=True)
    weight = models.FloatField()
    
    # Reference to original import/export item
    import_item_reference = models.ForeignKey(
        'imports.Item', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        help_text="Reference to original import item"
    )
    export_item_reference = models.ForeignKey(
        'exports.Item', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        help_text="Reference to original export item"
    )

    class Meta:
        verbose_name = "Consolidated Item"
        verbose_name_plural = "Consolidated Items"
        ordering = ("name",)

    def __str__(self):
        return f"{self.shipment.get_shipment_type_display()} - {self.name} (x{self.quantity})"

    def get_absolute_url(self):
        return reverse("consolidated_item_detail", kwargs={"id": self.id})

    def save(self, *args, **kwargs):
        # Calculate total price
        if self.price and self.quantity:
            self.total_price = self.price * self.quantity
        return super().save(*args, **kwargs)
