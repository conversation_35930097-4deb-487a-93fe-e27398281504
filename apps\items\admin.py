from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import ConsolidatedItem


@admin.register(ConsolidatedItem)
class ConsolidatedItemAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'item_type', 'customer_name', 'tracking_no', 'quantity', 
        'price', 'total_price', 'weight', 'shipping_status', 'payment_status',
        'shipped_at', 'created_on'
    )
    
    list_filter = (
        'item_type', 'shipping_status', 'payment_status', 'weight_unit',
        'created_on', 'shipped_at'
    )
    
    search_fields = (
        'name', 'tracking_no', 'customer__first_name', 'customer__last_name',
        'customer__email', 'payment_reference'
    )
    
    readonly_fields = (
        'total_price', 'volumetric_weight', 'customer_name', 'source_link',
        'created_on', 'updated_on'
    )
    
    fieldsets = (
        ('Item Information', {
            'fields': ('name', 'item_type', 'quantity', 'price', 'total_price', 'weight')
        }),
        ('Customer & Shipping', {
            'fields': ('customer', 'customer_name', 'tracking_no', 'phone_no',
                       'sender', 'destination')
        }),
        ('Payment & Status', {
            'fields': ('payment_status', 'payment_reference', 'shipping_status',
                       'shipped_at')
        }),
        ('Shipment Details', {
            'fields': ('shipment_weight', 'weight_unit', 'length', 'breadth',
                       'height', 'volumetric_weight', 'volumetric_weight_unit',
                       'shipping_fee')
        }),
        ('Source Reference', {
            'fields': ('export', 'import_shipment', 'source_link')
        }),
        ('Timestamps', {
            'fields': ('created_on', 'updated_on'),
            'classes': ('collapse',)
        })
    )
    
    ordering = ('-created_on',)
    
    actions = ['sync_all_items', 'mark_as_shipped', 'mark_as_paid']
    
    def customer_name(self, obj):
        """Display customer full name"""
        return obj.customer_full_name
    customer_name.short_description = 'Customer'
    customer_name.admin_order_field = 'customer__last_name'
    
    def source_link(self, obj):
        """Create a link to the original export/import object"""
        if obj.item_type == 'export' and obj.export:
            url = reverse('admin:exports_export_change', args=[obj.export.id])
            return format_html('<a href="{}" target="_blank">View Export</a>', url)
        elif obj.item_type == 'import' and obj.import_shipment:
            url = reverse('admin:imports_import_change', args=[obj.import_shipment.id])
            return format_html('<a href="{}" target="_blank">View Import</a>', url)
        return '-'
    source_link.short_description = 'Source'
    
    def sync_all_items(self, request, queryset):
        """Sync all items from exports and imports"""
        from apps.exports.models import Item as ExportItem
        from apps.imports.models import Item as ImportItem
        
        synced_count = 0
        
        # Sync export items
        for export_item in ExportItem.objects.all():
            ConsolidatedItem.sync_from_export_item(export_item)
            synced_count += 1
        
        # Sync import items
        for import_item in ImportItem.objects.all():
            ConsolidatedItem.sync_from_import_item(import_item)
            synced_count += 1
        
        self.message_user(
            request, 
            f'Successfully synced {synced_count} items from exports and imports.'
        )
    sync_all_items.short_description = 'Sync all items from exports and imports'
    
    def mark_as_shipped(self, request, queryset):
        """Mark selected items as shipped"""
        from django.utils import timezone
        
        updated = queryset.update(
            shipping_status='Shipped',
            shipped_at=timezone.now()
        )
        
        # Update the original export/import objects as well
        for item in queryset:
            source_obj = item.source_object
            if source_obj:
                source_obj.shipping_status = 'Shipped'
                source_obj.shipped_at = timezone.now()
                source_obj.save()
        
        self.message_user(
            request,
            f'Successfully marked {updated} items as shipped.'
        )
    mark_as_shipped.short_description = 'Mark selected items as shipped'
    
    def mark_as_paid(self, request, queryset):
        """Mark selected items as paid"""
        updated = queryset.update(payment_status='Paid')
        
        # Update the original export/import objects as well
        for item in queryset:
            source_obj = item.source_object
            if source_obj:
                source_obj.payment_status = 'Paid'
                source_obj.save()
        
        self.message_user(
            request,
            f'Successfully marked {updated} items as paid.'
        )
    mark_as_paid.short_description = 'Mark selected items as paid'
    
    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related(
            'customer', 'sender', 'destination', 'export', 'import_shipment'
        )
