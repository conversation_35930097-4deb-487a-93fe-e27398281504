[{"model": "admin.logentry", "pk": 53, "fields": {"action_time": "2023-06-11T06:58:23.720Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "8ac17233-4cc1-4eb6-8a32-5e58c135fa47", "object_repr": "Test User TU_MOG973851", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Profile picture\"]}}]"}}, {"model": "admin.logentry", "pk": 54, "fields": {"action_time": "2023-06-11T07:01:10.662Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "ab14dd4b-908a-4f06-84e4-8e929c55e784", "object_repr": "Mog Dynamics MD_MOG141005", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 55, "fields": {"action_time": "2023-06-11T07:47:04.599Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "dbcb1b6c-fb9a-4e97-ad46-e27719b7aa10", "object_repr": "Mog Dynamics MD_MOG3319374", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 56, "fields": {"action_time": "2023-06-13T12:03:28.768Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 9, "object_id": "495bc7b1-f1c5-4b2a-9775-268be4ab19ad", "object_repr": "Test User TU_MOG973851 IMP1638583893", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Payment status\"]}}]"}}, {"model": "admin.logentry", "pk": 57, "fields": {"action_time": "2023-06-13T12:04:26.122Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 9, "object_id": "495bc7b1-f1c5-4b2a-9775-268be4ab19ad", "object_repr": "Test User TU_MOG973851 IMP1638583893", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Payment status\"]}}]"}}, {"model": "admin.logentry", "pk": 58, "fields": {"action_time": "2023-06-14T09:51:57.048Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "8ac17233-4cc1-4eb6-8a32-5e58c135fa47", "object_repr": "Test User TU_MOG5219992", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"password\"]}}]"}}, {"model": "admin.logentry", "pk": 59, "fields": {"action_time": "2023-06-14T09:52:01.195Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "8ac17233-4cc1-4eb6-8a32-5e58c135fa47", "object_repr": "Test User TU_MOG6574133", "action_flag": 2, "change_message": "[]"}}, {"model": "admin.logentry", "pk": 60, "fields": {"action_time": "2023-06-22T08:58:33.201Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "e8f85d44-5417-455d-a41c-78a64fb19e29", "object_repr": "Mog Dynamics MD_MOG4949144", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 61, "fields": {"action_time": "2023-06-22T09:00:16.667Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "f9d144d5-48e4-4c99-a426-7dc88861ee86", "object_repr": "Mog Dynamics MD_MOG2512796", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 62, "fields": {"action_time": "2023-06-22T09:03:50.308Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "576b8f28-2cf6-48f1-9c88-10bb2cad8260", "object_repr": "Mog Dynamics MD_MOG5750530", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 63, "fields": {"action_time": "2023-06-29T18:46:34.459Z", "user": "57e30c76-3c1f-4f26-9a4a-e5fe81bb5210", "content_type": 7, "object_id": "ca3f49f2-cc6b-4188-886b-914d7be6b8ee", "object_repr": "<PERSON><PERSON><PERSON> AO_MOG1915504 EXP1200417839", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Weight unit\", \"Volumentary weight unit\", \"Email sent\"]}}]"}}, {"model": "admin.logentry", "pk": 64, "fields": {"action_time": "2023-06-29T18:53:37.164Z", "user": "57e30c76-3c1f-4f26-9a4a-e5fe81bb5210", "content_type": 7, "object_id": "cfbb728a-7b1a-46c1-ae39-861ac8d92147", "object_repr": "Test User TU_MOG8314408 EXP1903069889", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Weight unit\", \"Volumentary weight unit\", \"Email sent\"]}}]"}}, {"model": "admin.logentry", "pk": 65, "fields": {"action_time": "2023-06-29T18:53:50.537Z", "user": "57e30c76-3c1f-4f26-9a4a-e5fe81bb5210", "content_type": 7, "object_id": "3d29070f-c9bf-472b-b1ca-b53f82f4adf4", "object_repr": "Test User TU_MOG8314408 EXP1318801344", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Weight unit\", \"Volumentary weight unit\", \"Email sent\"]}}]"}}, {"model": "admin.logentry", "pk": 66, "fields": {"action_time": "2023-06-29T18:54:02.048Z", "user": "57e30c76-3c1f-4f26-9a4a-e5fe81bb5210", "content_type": 7, "object_id": "ca3f49f2-cc6b-4188-886b-914d7be6b8ee", "object_repr": "<PERSON><PERSON><PERSON> AO_MOG1915504 EXP1200417839", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Email sent\"]}}]"}}, {"model": "admin.logentry", "pk": 67, "fields": {"action_time": "2023-06-29T19:16:02.088Z", "user": "57e30c76-3c1f-4f26-9a4a-e5fe81bb5210", "content_type": 7, "object_id": "cfbb728a-7b1a-46c1-ae39-861ac8d92147", "object_repr": "Test User TU_MOG8314408 EXP1903069889", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Email sent\"]}}]"}}, {"model": "admin.logentry", "pk": 68, "fields": {"action_time": "2023-08-18T07:36:39.355Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "e2f40258-a2db-4f0f-baec-79c4fbc4fcb5", "object_repr": "Dispatch Rider DR_MOG8131951", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 69, "fields": {"action_time": "2023-08-18T07:44:18.539Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "e2f40258-a2db-4f0f-baec-79c4fbc4fcb5", "object_repr": "Small Business SB_MOG3383", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"First name\", \"Last name\", \"Em<PERSON>\"]}}]"}}, {"model": "admin.logentry", "pk": 70, "fields": {"action_time": "2023-08-18T07:50:13.283Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "e2f40258-a2db-4f0f-baec-79c4fbc4fcb5", "object_repr": "Small Business SB_MOG2479720", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"User type\"]}}]"}}, {"model": "admin.logentry", "pk": 71, "fields": {"action_time": "2023-08-18T14:31:57.484Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 14, "object_id": "56f5275c-195b-48df-b0c5-c5fc5a7ea68b", "object_repr": "Small Business SB_MOG2479720 IMP1623940588", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 72, "fields": {"action_time": "2023-08-18T14:33:25.031Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 14, "object_id": "56f5275c-195b-48df-b0c5-c5fc5a7ea68b", "object_repr": "Small Business SB_MOG2479720 IMP1623940588", "action_flag": 2, "change_message": "[]"}}, {"model": "admin.logentry", "pk": 73, "fields": {"action_time": "2023-08-18T14:38:54.793Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 14, "object_id": "56f5275c-195b-48df-b0c5-c5fc5a7ea68b", "object_repr": "Small Business SB_MOG2479720 IMP1623940588", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Delivery fee\"]}}]"}}, {"model": "admin.logentry", "pk": 74, "fields": {"action_time": "2023-08-18T17:02:20.764Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "5dad5182-2e2d-482e-b96e-ac6012e59e75", "object_repr": "Dispatch Rider DR_MOG1383811", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 75, "fields": {"action_time": "2023-08-18T17:02:24.237Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "5dad5182-2e2d-482e-b96e-ac6012e59e75", "object_repr": "Dispatch Rider DR_MOG4287261", "action_flag": 2, "change_message": "[]"}}, {"model": "admin.logentry", "pk": 76, "fields": {"action_time": "2023-08-19T18:24:31.287Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 14, "object_id": "56f5275c-195b-48df-b0c5-c5fc5a7ea68b", "object_repr": "Small Business SB_MOG2479720 IMP1623940588", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Dispatcher\"]}}]"}}, {"model": "admin.logentry", "pk": 77, "fields": {"action_time": "2023-08-19T19:10:19.430Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "fa25b401-1944-45ef-8cbe-e43d0a4beaba", "object_repr": "bay technologies", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 78, "fields": {"action_time": "2023-08-19T19:15:15.729Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "e2f40258-a2db-4f0f-baec-79c4fbc4fcb5", "object_repr": "Small Business SB_MOG2479720", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 83, "fields": {"action_time": "2023-08-19T19:55:23.435Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "e2f40258-a2db-4f0f-baec-79c4fbc4fcb5", "object_repr": "Small Business SB_MOG2479720", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 84, "fields": {"action_time": "2023-08-19T20:33:31.005Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "fa25b401-1944-45ef-8cbe-e43d0a4beaba", "object_repr": "bay technologies", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"User\"]}}]"}}, {"model": "admin.logentry", "pk": 85, "fields": {"action_time": "2023-08-19T20:33:37.298Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "fa25b401-1944-45ef-8cbe-e43d0a4beaba", "object_repr": "bay technologies", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"User\"]}}]"}}, {"model": "admin.logentry", "pk": 86, "fields": {"action_time": "2023-08-19T20:33:59.408Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "fa25b401-1944-45ef-8cbe-e43d0a4beaba", "object_repr": "bay technologies", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 87, "fields": {"action_time": "2023-08-19T20:41:28.798Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "0399beaf-e2df-40a2-9cda-45ce67580a0b", "object_repr": "Small Business SB_MOG1095441", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 88, "fields": {"action_time": "2023-08-19T20:41:32.018Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "0399beaf-e2df-40a2-9cda-45ce67580a0b", "object_repr": "Small Business SB_MOG2620177", "action_flag": 2, "change_message": "[]"}}, {"model": "admin.logentry", "pk": 89, "fields": {"action_time": "2023-08-19T20:42:10.150Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "b3198eca-724e-42f6-88a6-de111eab175d", "object_repr": "bay technologies", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Business name\", \"Rc number\", \"Company address\", \"Cac certificate\"]}}]"}}, {"model": "admin.logentry", "pk": 90, "fields": {"action_time": "2023-08-19T20:47:25.622Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 14, "object_id": "af115ac6-e102-4007-9d03-7a40234916fe", "object_repr": "bay technologies LCL1984056723", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 91, "fields": {"action_time": "2023-08-19T20:47:51.405Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 14, "object_id": "af115ac6-e102-4007-9d03-7a40234916fe", "object_repr": "bay technologies LCL1984056723", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Delivery fee\"]}}]"}}, {"model": "admin.logentry", "pk": 92, "fields": {"action_time": "2023-08-20T20:58:25.570Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "b3198eca-724e-42f6-88a6-de111eab175d", "object_repr": "bay technologies", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Company address\"]}}]"}}, {"model": "admin.logentry", "pk": 93, "fields": {"action_time": "2023-08-20T21:06:59.200Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "b3198eca-724e-42f6-88a6-de111eab175d", "object_repr": "bay technologies", "action_flag": 2, "change_message": "[]"}}, {"model": "admin.logentry", "pk": 94, "fields": {"action_time": "2023-08-20T23:11:42.347Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 14, "object_id": "af115ac6-e102-4007-9d03-7a40234916fe", "object_repr": "bay technologies LCL1984056723", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Payment status\"]}}]"}}, {"model": "admin.logentry", "pk": 95, "fields": {"action_time": "2023-08-20T23:13:23.040Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 14, "object_id": "af115ac6-e102-4007-9d03-7a40234916fe", "object_repr": "bay technologies LCL1984056723", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Payment status\"]}}]"}}, {"model": "admin.logentry", "pk": 96, "fields": {"action_time": "2023-08-20T23:14:13.758Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 14, "object_id": "af115ac6-e102-4007-9d03-7a40234916fe", "object_repr": "bay technologies LCL1984056723", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Payment status\"]}}]"}}, {"model": "admin.logentry", "pk": 97, "fields": {"action_time": "2023-08-23T15:42:49.469Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 9, "object_id": "c11255fe-ef08-425b-9597-b9a7df38a03c", "object_repr": "<PERSON><PERSON><PERSON> AO_MOG1915504 IMP1584660310", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Shipping status\"]}}]"}}, {"model": "admin.logentry", "pk": 98, "fields": {"action_time": "2023-08-23T15:44:34.145Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 9, "object_id": "c11255fe-ef08-425b-9597-b9a7df38a03c", "object_repr": "<PERSON><PERSON><PERSON> AO_MOG1915504 IMP1584660310", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Shipping status\"]}}]"}}, {"model": "admin.logentry", "pk": 99, "fields": {"action_time": "2023-08-24T09:01:17.022Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "64a98708-26b6-4b3c-82b7-be0f1845177b", "object_repr": "Bay Tech BT_MOG1108735", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 100, "fields": {"action_time": "2023-08-24T09:13:12.745Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "93050e4b-02cb-454a-ab4a-ed68cafb9fe8", "object_repr": "Bay Tech BT_MOG9487291", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 101, "fields": {"action_time": "2023-08-24T09:23:56.092Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "da422de2-721e-4e2d-93bb-818396cec20f", "object_repr": "Bay Tech BT_MOG6084837", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 102, "fields": {"action_time": "2023-08-24T15:13:08.100Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "7175cde0-7681-4593-8f31-c1fd322d7748", "object_repr": "Bay Tech BT_MOG3305287", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 103, "fields": {"action_time": "2023-08-24T15:16:34.760Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "22db2ee0-3f9f-4d3f-9b37-90428750dbb4", "object_repr": "Bay Tech BT_MOG6460436", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 104, "fields": {"action_time": "2023-08-24T15:26:03.812Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "17f229ca-bbf5-486a-8161-f3861689922a", "object_repr": "Bay Tech BT_MOG5887024", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 105, "fields": {"action_time": "2023-08-24T15:46:17.118Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "b3198eca-724e-42f6-88a6-de111eab175d", "object_repr": "bay technologies", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Rc number\"]}}]"}}, {"model": "admin.logentry", "pk": 106, "fields": {"action_time": "2023-08-24T15:53:56.379Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "56e71617-5932-4782-a828-43918b06155a", "object_repr": "Bay Tech BT_MOG1977557", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 107, "fields": {"action_time": "2023-08-24T15:56:16.692Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "b3198eca-724e-42f6-88a6-de111eab175d", "object_repr": "bay technologies", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 108, "fields": {"action_time": "2023-08-24T16:41:42.537Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "be10135d-ebc6-43a5-9b16-f502cfbe8ac5", "object_repr": "Bay Tech BT_MOG4179235", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 109, "fields": {"action_time": "2023-08-25T09:21:14.800Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "f92eb51d-a801-42b4-821b-89e4d2811495", "object_repr": "Bay Tech BT_MOG8106991", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 110, "fields": {"action_time": "2023-08-25T09:21:41.196Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "0399beaf-e2df-40a2-9cda-45ce67580a0b", "object_repr": "Small Business SB_MOG8700553", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"User type\"]}}]"}}, {"model": "admin.logentry", "pk": 111, "fields": {"action_time": "2023-08-25T10:54:28.005Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "422fb43e-f666-47d3-8363-cef978fa7842", "object_repr": "Bay Tech BT_MOG6430748", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 112, "fields": {"action_time": "2023-08-25T10:58:04.600Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "7bea0899-ea01-4d0f-b519-09edcb209aba", "object_repr": "bay technologies", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 113, "fields": {"action_time": "2023-08-25T11:03:20.429Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "8eaca3de-5744-4de2-90ab-61872cc6e03c", "object_repr": "bay technology", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 114, "fields": {"action_time": "2023-08-25T11:08:35.666Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "8eaca3de-5744-4de2-90ab-61872cc6e03c", "object_repr": "Bay Technologies", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Business name\"]}}]"}}, {"model": "admin.logentry", "pk": 115, "fields": {"action_time": "2023-08-25T11:09:57.266Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "8eaca3de-5744-4de2-90ab-61872cc6e03c", "object_repr": "Bay Technologies", "action_flag": 2, "change_message": "[]"}}, {"model": "admin.logentry", "pk": 116, "fields": {"action_time": "2023-08-25T11:12:12.381Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "8eaca3de-5744-4de2-90ab-61872cc6e03c", "object_repr": "Bay Technologies", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 117, "fields": {"action_time": "2023-08-25T11:12:18.742Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "7bea0899-ea01-4d0f-b519-09edcb209aba", "object_repr": "bay technologies", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 118, "fields": {"action_time": "2023-08-25T11:15:40.105Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "ef105825-823b-4363-86d1-9e718a5a3333", "object_repr": "Bay Technologies", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 119, "fields": {"action_time": "2023-08-25T13:56:14.393Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 16, "object_id": "ef105825-823b-4363-86d1-9e718a5a3333", "object_repr": "Bay Technologies", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 120, "fields": {"action_time": "2023-08-25T14:05:21.403Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "c9c2fb82-a023-4699-a310-eb84424e9f75", "object_repr": "Bay Technologies BT_MOG9510402", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 121, "fields": {"action_time": "2023-08-25T14:05:21.558Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "642a2296-9580-4eb0-b7aa-3a5d5ee6a523", "object_repr": "Dhdjkhdjkhhjk Hjkhjkhjk DH_MOG3369319", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 122, "fields": {"action_time": "2023-08-25T14:16:18.467Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "d1c20396-f694-4d60-8755-a4ab3f2e86db", "object_repr": "Bay Tech BT_MOG798510", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Phone no\", \"Is active\"]}}]"}}, {"model": "admin.logentry", "pk": 123, "fields": {"action_time": "2023-08-25T14:18:33.708Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 6, "object_id": "d1c20396-f694-4d60-8755-a4ab3f2e86db", "object_repr": "Bay Tech BT_MOG4166022", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"password\"]}}]"}}, {"model": "admin.logentry", "pk": 124, "fields": {"action_time": "2023-08-25T15:09:48.722Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 14, "object_id": "1168a77c-a0fd-42b9-a19d-f7c5282beb7d", "object_repr": "bay technologies LCL1858943500", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 125, "fields": {"action_time": "2023-08-25T15:34:14.267Z", "user": "08878e09-2a2c-4983-8267-fa9c507d1447", "content_type": 14, "object_id": "1168a77c-a0fd-42b9-a19d-f7c5282beb7d", "object_repr": "bay technologies LCL1858943500", "action_flag": 2, "change_message": "[]"}}, {"model": "auth.permission", "pk": 1, "fields": {"name": "Can add log entry", "content_type": 1, "codename": "add_logentry"}}, {"model": "auth.permission", "pk": 2, "fields": {"name": "Can change log entry", "content_type": 1, "codename": "change_logentry"}}, {"model": "auth.permission", "pk": 3, "fields": {"name": "Can delete log entry", "content_type": 1, "codename": "delete_logentry"}}, {"model": "auth.permission", "pk": 4, "fields": {"name": "Can view log entry", "content_type": 1, "codename": "view_logentry"}}, {"model": "auth.permission", "pk": 5, "fields": {"name": "Can add permission", "content_type": 2, "codename": "add_permission"}}, {"model": "auth.permission", "pk": 6, "fields": {"name": "Can change permission", "content_type": 2, "codename": "change_permission"}}, {"model": "auth.permission", "pk": 7, "fields": {"name": "Can delete permission", "content_type": 2, "codename": "delete_permission"}}, {"model": "auth.permission", "pk": 8, "fields": {"name": "Can view permission", "content_type": 2, "codename": "view_permission"}}, {"model": "auth.permission", "pk": 9, "fields": {"name": "Can add group", "content_type": 3, "codename": "add_group"}}, {"model": "auth.permission", "pk": 10, "fields": {"name": "Can change group", "content_type": 3, "codename": "change_group"}}, {"model": "auth.permission", "pk": 11, "fields": {"name": "Can delete group", "content_type": 3, "codename": "delete_group"}}, {"model": "auth.permission", "pk": 12, "fields": {"name": "Can view group", "content_type": 3, "codename": "view_group"}}, {"model": "auth.permission", "pk": 13, "fields": {"name": "Can add content type", "content_type": 4, "codename": "add_contenttype"}}, {"model": "auth.permission", "pk": 14, "fields": {"name": "Can change content type", "content_type": 4, "codename": "change_contenttype"}}, {"model": "auth.permission", "pk": 15, "fields": {"name": "Can delete content type", "content_type": 4, "codename": "delete_contenttype"}}, {"model": "auth.permission", "pk": 16, "fields": {"name": "Can view content type", "content_type": 4, "codename": "view_contenttype"}}, {"model": "auth.permission", "pk": 17, "fields": {"name": "Can add session", "content_type": 5, "codename": "add_session"}}, {"model": "auth.permission", "pk": 18, "fields": {"name": "Can change session", "content_type": 5, "codename": "change_session"}}, {"model": "auth.permission", "pk": 19, "fields": {"name": "Can delete session", "content_type": 5, "codename": "delete_session"}}, {"model": "auth.permission", "pk": 20, "fields": {"name": "Can view session", "content_type": 5, "codename": "view_session"}}, {"model": "auth.permission", "pk": 21, "fields": {"name": "Can add User", "content_type": 6, "codename": "add_customuser"}}, {"model": "auth.permission", "pk": 22, "fields": {"name": "Can change User", "content_type": 6, "codename": "change_customuser"}}, {"model": "auth.permission", "pk": 23, "fields": {"name": "Can delete User", "content_type": 6, "codename": "delete_customuser"}}, {"model": "auth.permission", "pk": 24, "fields": {"name": "Can view User", "content_type": 6, "codename": "view_customuser"}}, {"model": "auth.permission", "pk": 25, "fields": {"name": "Can add Export", "content_type": 7, "codename": "add_export"}}, {"model": "auth.permission", "pk": 26, "fields": {"name": "Can change Export", "content_type": 7, "codename": "change_export"}}, {"model": "auth.permission", "pk": 27, "fields": {"name": "Can delete Export", "content_type": 7, "codename": "delete_export"}}, {"model": "auth.permission", "pk": 28, "fields": {"name": "Can view Export", "content_type": 7, "codename": "view_export"}}, {"model": "auth.permission", "pk": 29, "fields": {"name": "Can add Item", "content_type": 8, "codename": "add_item"}}, {"model": "auth.permission", "pk": 30, "fields": {"name": "Can change Item", "content_type": 8, "codename": "change_item"}}, {"model": "auth.permission", "pk": 31, "fields": {"name": "Can delete Item", "content_type": 8, "codename": "delete_item"}}, {"model": "auth.permission", "pk": 32, "fields": {"name": "Can view Item", "content_type": 8, "codename": "view_item"}}, {"model": "auth.permission", "pk": 33, "fields": {"name": "Can add Import", "content_type": 9, "codename": "add_import"}}, {"model": "auth.permission", "pk": 34, "fields": {"name": "Can change Import", "content_type": 9, "codename": "change_import"}}, {"model": "auth.permission", "pk": 35, "fields": {"name": "Can delete Import", "content_type": 9, "codename": "delete_import"}}, {"model": "auth.permission", "pk": 36, "fields": {"name": "Can view Import", "content_type": 9, "codename": "view_import"}}, {"model": "auth.permission", "pk": 37, "fields": {"name": "Can add Item", "content_type": 10, "codename": "add_item"}}, {"model": "auth.permission", "pk": 38, "fields": {"name": "Can change Item", "content_type": 10, "codename": "change_item"}}, {"model": "auth.permission", "pk": 39, "fields": {"name": "Can delete Item", "content_type": 10, "codename": "delete_item"}}, {"model": "auth.permission", "pk": 40, "fields": {"name": "Can view Item", "content_type": 10, "codename": "view_item"}}, {"model": "auth.permission", "pk": 41, "fields": {"name": "Can add address", "content_type": 11, "codename": "add_address"}}, {"model": "auth.permission", "pk": 42, "fields": {"name": "Can change address", "content_type": 11, "codename": "change_address"}}, {"model": "auth.permission", "pk": 43, "fields": {"name": "Can delete address", "content_type": 11, "codename": "delete_address"}}, {"model": "auth.permission", "pk": 44, "fields": {"name": "Can view address", "content_type": 11, "codename": "view_address"}}, {"model": "auth.permission", "pk": 45, "fields": {"name": "Can add Pricing", "content_type": 12, "codename": "add_pricing"}}, {"model": "auth.permission", "pk": 46, "fields": {"name": "Can change Pricing", "content_type": 12, "codename": "change_pricing"}}, {"model": "auth.permission", "pk": 47, "fields": {"name": "Can delete Pricing", "content_type": 12, "codename": "delete_pricing"}}, {"model": "auth.permission", "pk": 48, "fields": {"name": "Can view Pricing", "content_type": 12, "codename": "view_pricing"}}, {"model": "auth.permission", "pk": 49, "fields": {"name": "Can add Gallery", "content_type": 13, "codename": "add_gallery"}}, {"model": "auth.permission", "pk": 50, "fields": {"name": "Can change Gallery", "content_type": 13, "codename": "change_gallery"}}, {"model": "auth.permission", "pk": 51, "fields": {"name": "Can delete Gallery", "content_type": 13, "codename": "delete_gallery"}}, {"model": "auth.permission", "pk": 52, "fields": {"name": "Can view Gallery", "content_type": 13, "codename": "view_gallery"}}, {"model": "auth.permission", "pk": 53, "fields": {"name": "Can add Logistic", "content_type": 14, "codename": "add_logistic"}}, {"model": "auth.permission", "pk": 54, "fields": {"name": "Can change Logistic", "content_type": 14, "codename": "change_logistic"}}, {"model": "auth.permission", "pk": 55, "fields": {"name": "Can delete Logistic", "content_type": 14, "codename": "delete_logistic"}}, {"model": "auth.permission", "pk": 56, "fields": {"name": "Can view Logistic", "content_type": 14, "codename": "view_logistic"}}, {"model": "auth.permission", "pk": 57, "fields": {"name": "Can add Item", "content_type": 15, "codename": "add_item"}}, {"model": "auth.permission", "pk": 58, "fields": {"name": "Can change Item", "content_type": 15, "codename": "change_item"}}, {"model": "auth.permission", "pk": 59, "fields": {"name": "Can delete Item", "content_type": 15, "codename": "delete_item"}}, {"model": "auth.permission", "pk": 60, "fields": {"name": "Can view Item", "content_type": 15, "codename": "view_item"}}, {"model": "auth.permission", "pk": 61, "fields": {"name": "Can add Company", "content_type": 16, "codename": "add_company"}}, {"model": "auth.permission", "pk": 62, "fields": {"name": "Can change Company", "content_type": 16, "codename": "change_company"}}, {"model": "auth.permission", "pk": 63, "fields": {"name": "Can delete Company", "content_type": 16, "codename": "delete_company"}}, {"model": "auth.permission", "pk": 64, "fields": {"name": "Can view Company", "content_type": 16, "codename": "view_company"}}, {"model": "contenttypes.contenttype", "pk": 1, "fields": {"app_label": "admin", "model": "logentry"}}, {"model": "contenttypes.contenttype", "pk": 2, "fields": {"app_label": "auth", "model": "permission"}}, {"model": "contenttypes.contenttype", "pk": 3, "fields": {"app_label": "auth", "model": "group"}}, {"model": "contenttypes.contenttype", "pk": 4, "fields": {"app_label": "contenttypes", "model": "contenttype"}}, {"model": "contenttypes.contenttype", "pk": 5, "fields": {"app_label": "sessions", "model": "session"}}, {"model": "contenttypes.contenttype", "pk": 6, "fields": {"app_label": "users", "model": "customuser"}}, {"model": "contenttypes.contenttype", "pk": 7, "fields": {"app_label": "exports", "model": "export"}}, {"model": "contenttypes.contenttype", "pk": 8, "fields": {"app_label": "exports", "model": "item"}}, {"model": "contenttypes.contenttype", "pk": 9, "fields": {"app_label": "imports", "model": "import"}}, {"model": "contenttypes.contenttype", "pk": 10, "fields": {"app_label": "imports", "model": "item"}}, {"model": "contenttypes.contenttype", "pk": 11, "fields": {"app_label": "addresses", "model": "address"}}, {"model": "contenttypes.contenttype", "pk": 12, "fields": {"app_label": "pricing", "model": "pricing"}}, {"model": "contenttypes.contenttype", "pk": 13, "fields": {"app_label": "galleries", "model": "gallery"}}, {"model": "contenttypes.contenttype", "pk": 14, "fields": {"app_label": "logistics", "model": "logistic"}}, {"model": "contenttypes.contenttype", "pk": 15, "fields": {"app_label": "logistics", "model": "item"}}, {"model": "contenttypes.contenttype", "pk": 16, "fields": {"app_label": "company", "model": "company"}}, {"model": "sessions.session", "pk": "4jtlsadr8kqar58t3fpebcuglcrssqmp", "fields": {"session_data": ".eJxVzDkOwjAQheG7uCbW2PE2lPScIRrP2IRFiZSlQtydREoB9fv-91YdrUvfrXOZuruos4KUYiqAjSXLjcPUNsmG2FRC9hDFOBfV6TfLxM8y7K08aLiNmsdhme5Z70Qf66yvo5TX5bB_Bz3N_VY7EwErZgeJKDEaH0E4meAFW7alSiXBSEw1AKF1LBtrfQmQI7qqPl9lZ0DZ:1qYpzO:gE8rO6YS6W-3qrG6Z93OLjDOWfgxPjNA8uQq0kc0FBc", "expire_date": "2023-09-06T15:41:46.422Z"}}, {"model": "sessions.session", "pk": "9dqi7xmfa6gcxuqhxbzv5fkfxudvaok7", "fields": {"session_data": ".eJxVzDkOwjAQheG7uCbW2PE2lPScIRrP2IRFiZSlQtydREoB9fv-91YdrUvfrXOZuruos4KUYiqAjSXLjcPUNsmG2FRC9hDFOBfV6TfLxM8y7K08aLiNmsdhme5Z70Qf66yvo5TX5bB_Bz3N_VY7EwErZgeJKDEaH0E4meAFW7alSiXBSEw1AKF1LBtrfQmQI7qqPl9lZ0DZ:1qZXdx:ggP8YZFC3y0muGTRZUKmzEjkkKY6NpoERLZ61eenE_M", "expire_date": "2023-09-08T14:18:33.791Z"}}, {"model": "sessions.session", "pk": "a0lflpnhg5bqdlmhn5cnpq8grd9mm4bs", "fields": {"session_data": ".eJxVjjkOwjAUBe_iGluOV0xJzxmiv5kEUCJlqRB3J5FSQP1mRu-tWliXrl1nmdqe1UVZXwoKVC2Oqw4WnC7EoEMkSTmeLVhUp18NgZ4y7C4_YLiPhsZhmXo0O2KOdTa3keV1Pdi_QAdzt9kui-RMTRNc9sknhiQciWMUkAJiycGZPObMKTnZTsZAlRJWjwwV1ecLaJNDSw:1qZUqW:DNA6ymv6ylhxxbsfZUM7kmsmn_2n3ghNgDFoAq-1Y1U", "expire_date": "2023-09-08T11:19:20.195Z"}}, {"model": "sessions.session", "pk": "cnukdquvjvijzlw4p24c2ujzqskxxh68", "fields": {"session_data": ".eJxVjjsOwyAQRO9CHSOWXQykTJ8zWLBe4nxkS_5UUe4eLLlIupHevNG8VZe2dei2Rebu3quzConBW8SGmKEhyW0TEtrGiQsM6Eoir06_Wk78lHF3-0cab5PmaVzne9Z7RR900depl9fl6P4NDGkZqm0xQ0bhmAiI0EQfBIKNUpjReNMSFRsL1h8CUIOJmTOHVqKrIKrPF7cPQGU:1qDjt0:PgWUkS4xPdFyUcUM45DXg2lojxhs1BDZsjKGlLRqynI", "expire_date": "2023-07-10T10:55:58.921Z"}}, {"model": "sessions.session", "pk": "ekrcsuee0alwsj6i82iik861jlnvgip6", "fields": {"session_data": ".eJxVzDkOwjAQheG7uCbW2PE2lPScIRrP2IRFiZSlQtydREoB9fv-91YdrUvfrXOZuruos4KUYiqAjSXLjcPUNsmG2FRC9hDFOBfV6TfLxM8y7K08aLiNmsdhme5Z70Qf66yvo5TX5bB_Bz3N_VY7EwErZgeJKDEaH0E4meAFW7alSiXBSEw1AKF1LBtrfQmQI7qqPl9lZ0DZ:1qV9ed:l2A4880U0eFTXRoSj-Fx-wprIZcprLVyj9e4lVlbCsw", "expire_date": "2023-08-27T11:53:07.874Z"}}, {"model": "sessions.session", "pk": "ezz77p58a40zmpw25lwhy1wl1bag0iz9", "fields": {"session_data": ".eJxVj81OxDAMBt8lZ1qlzZ_NkTvPEDl2sl1AzZJ0xQHx7qTSHuDsmfnkbxXpfmzx3nOLV1HPSgMEyBqnlVaeLIKZYPVhKoTsdJDF2qCe_mqJ-D3vpytvtF_qzHU_2jXNJzI_rn1-rZI_Xh7sv8BGfRu2XYLGgslqIALGxQUtDIt3gobXXKSQYCCm4jXhalkGZlz2OgW05YzeqPev2iS23PMRjzrWRjp9XqCNbwhLMkJJApBJ7LMDr8U5cQLWOPXzCx1bVjg:1qE6HL:oZUQreXhGVXB0Kqxo_gSu1ii7qfXrqGtXPSBLOgFDes", "expire_date": "2023-07-11T10:50:35.788Z"}}, {"model": "sessions.session", "pk": "o64ojsfwgnnc1saausct7tcbp1zc7fv0", "fields": {"session_data": ".eJxVjUsOgzAMBe-SdYNsQ2yny-57BmSSUPoRSHxWVe9eIbFo129m3tu1tq1Duy1lbu_ZnR2oihaInoySb6LWXonF9xZTAMnYNOJOv1pn6VnG3c0PG29TlaZxne9dtSPVsS7VdcrldTnYv8Bgy7A_k5QgZkTCUgPXbFgQDXsMzCjWKWNmQuhBEmIkTKIZtEiOQcB9viY3Pqg:1qBYLg:r_iFj2KwQvVIeZ_NcNTg05aDuOIQ6H9HBz4aa56l3Kk", "expire_date": "2023-07-04T10:12:32.295Z"}}, {"model": "sessions.session", "pk": "prgw4f37qjd17kzm175fdg0693hpw43a", "fields": {"session_data": ".eJxVjUsOgzAMBe-SdYNsQ2yny-57BmSSUPoRSHxWVe9eIbFo129m3tu1tq1Duy1lbu_ZnR2oihaInoySb6LWXonF9xZTAMnYNOJOv1pn6VnG3c0PG29TlaZxne9dtSPVsS7VdcrldTnYv8Bgy7A_k5QgZkTCUgPXbFgQDXsMzCjWKWNmQuhBEmIkTKIZtEiOQcB9viY3Pqg:1q9NAT:hFQ1Tr-D_WxWcCr4behZVyIDIwV_s0n5CutFAABwk0w", "expire_date": "2023-06-28T09:51:57.225Z"}}, {"model": "sessions.session", "pk": "t0dfqy6nu4acrfym9y8m0x8685ve4dkf", "fields": {"session_data": ".eJxVjDsOwjAQBe_iGlv-rh1Kes4Q7dprEkCxlE-FuDtESgHtm5n3Ej1u69BvC8_9WMRZhMhO5wjSZVOlrxZkhx4lh8rJEAVrtDj9ZoT5wdPeljtOt6Zym9Z5JLUr6qCLurbCz8vh_h0MuAzfmisY0o6rT5CZjS8cOnbRIwI5jyUmBG2zthpsAetywEKJwfi0j-L9AQROQSo:1qSIAM:HvEFNsnnRfFR6MbvobyixcQ8BVc_ToMXm-VC6dP_vZ8", "expire_date": "2023-08-19T14:22:02.047Z"}}, {"model": "sessions.session", "pk": "ugogsr6zzw7l8ujlp8i5d70hrf37anqa", "fields": {"session_data": "eyJfcGFzc3dvcmRfcmVzZXRfdG9rZW4iOiJicTcyamYtN2M4ODNmZWY4MmJjMTk5NWU2OTFiYTgzODlhNzBlMjMifQ:1qCJ9t:-4IzYdrj7OhPo9QmOUeu_XE1hYPjRNmShK9JT4JvuDw", "expire_date": "2023-07-06T12:11:29.293Z"}}, {"model": "sessions.session", "pk": "w498lpcchtt877c55lmxjmb2bm83vxx9", "fields": {"session_data": "eyJfcGFzc3dvcmRfcmVzZXRfdG9rZW4iOiJicThvNHYtYjcwYmIwNjI1MDdjNzJkNDUwOTI0ODZmZTM1NTE2MjkifQ:1qCcTW:XYPUPX_EReE_EMO_tUa-HkdGlUV9s3NsJFMEIf-1avc", "expire_date": "2023-07-07T08:49:02.364Z"}}, {"model": "sessions.session", "pk": "wwsrfh087i88keiz2bsm8o6arklwysjg", "fields": {"session_data": ".eJxVjDsOwjAQBe_iGlv-rh1Kes4Q7dprEkCxlE-FuDtESgHtm5n3Ej1u69BvC8_9WMRZhMhO5wjSZVOlrxZkhx4lh8rJEAVrtDj9ZoT5wdPeljtOt6Zym9Z5JLUr6qCLurbCz8vh_h0MuAzfmisY0o6rT5CZjS8cOnbRIwI5jyUmBG2zthpsAetywEKJwfi0j-L9AQROQSo:1qCdva:dUh7PFS62GFuvJpca5hlmR6aPyAX_8X9vy8zVJ_oixI", "expire_date": "2023-07-07T10:22:06.213Z"}}, {"model": "sessions.session", "pk": "xlxmnio1mrn5r4anccdf56ugz8qqy930", "fields": {"session_data": ".eJxVzDkOwjAQheG7uCbW2PE2lPScIRrP2IRFiZSlQtydREoB9fv-91YdrUvfrXOZuruos4KUYiqAjSXLjcPUNsmG2FRC9hDFOBfV6TfLxM8y7K08aLiNmsdhme5Z70Qf66yvo5TX5bB_Bz3N_VY7EwErZgeJKDEaH0E4meAFW7alSiXBSEw1AKF1LBtrfQmQI7qqPl9lZ0DZ:1qXQbx:jhTtabzc2Rs6bJDIg1zemlVx1z3KenhckcN8MXXQQWw", "expire_date": "2023-09-02T18:23:45.702Z"}}, {"model": "users.customuser", "pk": "0399beaf-e2df-40a2-9cda-45ce67580a0b", "fields": {"password": "pbkdf2_sha256$600000$DUevjfkSQrHHYfe2btpSZy$DK9tJX0ctJHks402Zqb3lTrOC6E6tpG7G0tNMyW/j0w=", "last_login": "2023-08-25T11:48:54.530Z", "created_on": "2023-08-19T20:41:28.782Z", "updated_on": "2023-08-25T09:21:41.185Z", "profile_code": "SB_MOG8700553", "first_name": "small", "last_name": "business", "email": "<EMAIL>", "gender": "male", "phone_no": "+2348045284646", "address": "small business address", "city": "small business street", "state": "small business state", "country": "Nigeria", "document_type": "voters card", "document": "user_documents/Screenshot_from_2023-08-17_18-02-49_53sZJyQ.png", "profile_picture": "profile_images/Screenshot_from_2023-08-15_17-01-54_D4xDlt2.png", "user_type": "small business", "is_active": true, "is_staff": false, "is_superuser": false, "groups": [], "user_permissions": []}}, {"model": "users.customuser", "pk": "08878e09-2a2c-4983-8267-fa9c507d1447", "fields": {"password": "pbkdf2_sha256$600000$oCFKiCzRWdc4VRAvNyTdp7$jpMY20+9BTAkoCeQ+2nWUhq93WHxfO15MmpoOMJoqtA=", "last_login": "2023-08-27T12:33:38.319Z", "created_on": "2023-06-06T15:42:31.776Z", "updated_on": "2023-06-24T19:52:50.646Z", "profile_code": "AO_MOG1915504", "first_name": "a<PERSON><PERSON>", "last_name": "o<PERSON>u", "email": "<EMAIL>", "gender": "male", "phone_no": "+2348188746353", "address": "23 ife block street", "city": "egan", "state": "lagos", "country": "Nigeria", "document_type": "drivers licence", "document": "user_documents/Screenshot_from_2023-05-30_18-30-11.png", "profile_picture": "profile_images/test-vs-dev.jpeg", "user_type": "regular", "is_active": true, "is_staff": true, "is_superuser": true, "groups": [], "user_permissions": []}}, {"model": "users.customuser", "pk": "57e30c76-3c1f-4f26-9a4a-e5fe81bb5210", "fields": {"password": "pbkdf2_sha256$600000$2flUtseGEPCRgASmopnwNy$md2afNNcB44t/EwGrY8chRi/7lngX/47zriUVvBviO0=", "last_login": "2023-08-13T12:17:11.931Z", "created_on": "2023-06-22T09:04:00.065Z", "updated_on": "2023-06-22T09:04:00.600Z", "profile_code": "MD_MOG2790235", "first_name": "mog", "last_name": "dynamics", "email": "<EMAIL>", "gender": "male", "phone_no": "", "address": "", "city": "", "state": "", "country": "", "document_type": "", "document": "", "profile_picture": "dummy_qr.png", "user_type": "regular", "is_active": true, "is_staff": true, "is_superuser": true, "groups": [], "user_permissions": []}}, {"model": "users.customuser", "pk": "5dad5182-2e2d-482e-b96e-ac6012e59e75", "fields": {"password": "pbkdf2_sha256$600000$eq87S0wyMhZT6P7tED3sD8$0gcY+yC59O8RdqBN9PFXVN/KLkSO8YV7T3tOb4xHUv8=", "last_login": "2023-08-27T11:27:30.506Z", "created_on": "2023-08-18T17:02:20.677Z", "updated_on": "2023-08-18T17:02:24.229Z", "profile_code": "DR_MOG4287261", "first_name": "dispatch", "last_name": "rider", "email": "<EMAIL>", "gender": "male", "phone_no": "+2347034637373", "address": "dispatch rider address", "city": "dispatch rider city", "state": "dispatch rider state", "country": "Nigeria", "document_type": "voters card", "document": "user_documents/Screenshot_from_2023-08-17_18-02-49_ZPCPKEy.png", "profile_picture": "profile_images/Screenshot_from_2023-08-15_17-01-54.png", "user_type": "dispatcher", "is_active": true, "is_staff": false, "is_superuser": false, "groups": [], "user_permissions": []}}, {"model": "users.customuser", "pk": "8ac17233-4cc1-4eb6-8a32-5e58c135fa47", "fields": {"password": "pbkdf2_sha256$600000$2n5lPuAMDyko5wP11dkELY$jDcudHCJl+I06U6/Dmo4W/OPiO9JRBpEEopFTajfIco=", "last_login": "2023-08-18T07:27:24.429Z", "created_on": "2023-06-06T15:42:55.933Z", "updated_on": "2023-06-20T06:46:46.207Z", "profile_code": "TU_MOG8314408", "first_name": "test", "last_name": "user", "email": "<EMAIL>", "gender": "male", "phone_no": "", "address": "test user address", "city": "test user city", "state": "test user state", "country": "Nigeria", "document_type": "voters card", "document": "user_documents/Screenshot_from_2023-05-30_18-32-56.png", "profile_picture": "profile_images/Screenshot_from_2023-06-14_07-22-40.png", "user_type": "regular", "is_active": true, "is_staff": false, "is_superuser": false, "groups": [], "user_permissions": []}}, {"model": "users.customuser", "pk": "d1c20396-f694-4d60-8755-a4ab3f2e86db", "fields": {"password": "pbkdf2_sha256$600000$1P9C9czlI72gIHuofky10g$1Vjybv1II+HaZ+pP1qgQwBIaPz90N8VE8TNOBInM70c=", "last_login": "2023-08-27T11:28:46.365Z", "created_on": "2023-08-25T14:09:54.205Z", "updated_on": "2023-08-25T14:18:33.688Z", "profile_code": "BT_MOG4166022", "first_name": "bay", "last_name": "tech", "email": "<EMAIL>", "gender": "male", "phone_no": "+2347046874678", "address": "bay tech street", "city": "some city", "state": "some state", "country": "Azerbaijan", "document_type": "voters card", "document": "user_documents/Screenshot_from_2023-08-23_18-40-18_Nn5xclz.png", "profile_picture": "dummy_qr.png", "user_type": "small business", "is_active": true, "is_staff": false, "is_superuser": false, "groups": [], "user_permissions": []}}, {"model": "company.company", "pk": "9c153740-02d7-46a1-8e02-226541dcb618", "fields": {"created_on": "2023-08-25T14:09:54.273Z", "updated_on": "2023-08-25T14:09:54.273Z", "user": "d1c20396-f694-4d60-8755-a4ab3f2e86db", "business_name": "bay technologies", "rc_number": "1234567", "company_address": "address", "cac_certificate": "company_documents/Screenshot_from_2023-08-19_09-24-16.png"}}, {"model": "exports.export", "pk": "3d29070f-c9bf-472b-b1ca-b53f82f4adf4", "fields": {"created_on": "2023-06-06T16:22:51.505Z", "updated_on": "2023-06-29T19:51:21.992Z", "customer": "8ac17233-4cc1-4eb6-8a32-5e58c135fa47", "sender": "0e958174-547e-4b21-a582-e13e61e4296e", "receiver_name": "ahmed johnson", "receiver_address": "34 tiger street", "receiver_city": "nevaland", "receiver_state": "california", "receiver_country": "USA", "phone_no": "+***********", "weight": "5.00", "weight_unit": "kg", "lenght": 0.0, "breadth": 0.0, "height": 0.0, "volumentary_weight": 0.0, "volumentary_weight_unit": "kg", "export_fee": "500000.00", "tracking_no": "EXP1318801344", "payment_status": "unpaid", "payment_reference": "*********", "shipped_at": null, "tracking_url": "", "shipping_status": "cancelled", "attention": "", "email_sent": false}}, {"model": "exports.export", "pk": "ca3f49f2-cc6b-4188-886b-914d7be6b8ee", "fields": {"created_on": "2023-06-06T16:25:55.108Z", "updated_on": "2023-06-29T19:51:21.958Z", "customer": "08878e09-2a2c-4983-8267-fa9c507d1447", "sender": "0e958174-547e-4b21-a582-e13e61e4296e", "receiver_name": "tija<PERSON>", "receiver_address": "34 benson street", "receiver_city": "salt and pepper", "receiver_state": "<PERSON><PERSON><PERSON>", "receiver_country": "Canada", "phone_no": "+14842013737", "weight": "10.00", "weight_unit": "kg", "lenght": 0.0, "breadth": 0.0, "height": 0.0, "volumentary_weight": 0.0, "volumentary_weight_unit": "kg", "export_fee": "45300.00", "tracking_no": "EXP1200417839", "payment_status": "unpaid", "payment_reference": null, "shipped_at": null, "tracking_url": "", "shipping_status": "received.", "attention": "", "email_sent": false}}, {"model": "exports.export", "pk": "cfbb728a-7b1a-46c1-ae39-861ac8d92147", "fields": {"created_on": "2023-06-06T16:19:24.955Z", "updated_on": "2023-06-29T19:51:22Z", "customer": "8ac17233-4cc1-4eb6-8a32-5e58c135fa47", "sender": "0e958174-547e-4b21-a582-e13e61e4296e", "receiver_name": "tija<PERSON>", "receiver_address": "34 benson street", "receiver_city": "salt and pepper", "receiver_state": "<PERSON><PERSON><PERSON>", "receiver_country": "Canada", "phone_no": "+14842013737", "weight": "7.00", "weight_unit": "kg", "lenght": 0.0, "breadth": 0.0, "height": 0.0, "volumentary_weight": 0.0, "volumentary_weight_unit": "kg", "export_fee": "50000.00", "tracking_no": "EXP1903069889", "payment_status": "unpaid", "payment_reference": "682060761", "shipped_at": null, "tracking_url": "", "shipping_status": "pending", "attention": "", "email_sent": false}}, {"model": "exports.export", "pk": "d55f386a-1349-43c9-9473-090dc347b71e", "fields": {"created_on": "2023-06-06T16:11:37.822Z", "updated_on": "2023-06-29T19:51:22.008Z", "customer": "08878e09-2a2c-4983-8267-fa9c507d1447", "sender": "0e958174-547e-4b21-a582-e13e61e4296e", "receiver_name": "ahmed johnson", "receiver_address": "34 tiger street", "receiver_city": "nevaland", "receiver_state": "california", "receiver_country": "USA", "phone_no": "+***********", "weight": "5.00", "weight_unit": "kg", "lenght": 0.0, "breadth": 0.0, "height": 0.0, "volumentary_weight": 0.0, "volumentary_weight_unit": "kg", "export_fee": "36400.00", "tracking_no": "EXP1147378062", "payment_status": "paid", "payment_reference": "1693477572", "shipped_at": null, "tracking_url": "https://google.com", "shipping_status": "pending", "attention": "", "email_sent": false}}, {"model": "exports.item", "pk": "3d74148c-d6b2-46f4-b428-6dfc0cf17208", "fields": {"created_on": "2023-06-06T15:35:48.332Z", "updated_on": "2023-06-06T16:20:54.406Z", "export": "cfbb728a-7b1a-46c1-ae39-861ac8d92147", "name": "hp mouse", "quantity": 3, "price_currency": "USD", "price": "250.00", "total_price_currency": "USD", "total_price": "750.00", "weight": 1.0}}, {"model": "exports.item", "pk": "4856759f-b903-4d9b-90e5-b67e80661b59", "fields": {"created_on": "2023-06-06T15:38:51.232Z", "updated_on": "2023-06-06T16:21:28.751Z", "export": "d55f386a-1349-43c9-9473-090dc347b71e", "name": "dell alienware 2020", "quantity": 5, "price_currency": "USD", "price": "2500.00", "total_price_currency": "USD", "total_price": "12500.00", "weight": 1.5}}, {"model": "exports.item", "pk": "6e74f490-9dc0-48ed-8d20-6bfb11e4693a", "fields": {"created_on": "2023-06-06T15:40:38.328Z", "updated_on": "2023-06-06T16:21:01.372Z", "export": "cfbb728a-7b1a-46c1-ae39-861ac8d92147", "name": "samsun smart tv", "quantity": 2, "price_currency": "USD", "price": "2350.00", "total_price_currency": "USD", "total_price": "4700.00", "weight": 2.0}}, {"model": "exports.item", "pk": "8b3eb3cc-b6e1-4a44-91f0-a98bdb1f8481", "fields": {"created_on": "2023-06-06T15:38:07.337Z", "updated_on": "2023-06-06T16:21:09.170Z", "export": "cfbb728a-7b1a-46c1-ae39-861ac8d92147", "name": "galaxy tab", "quantity": 2, "price_currency": "USD", "price": "1450.00", "total_price_currency": "USD", "total_price": "2900.00", "weight": 1.0}}, {"model": "exports.item", "pk": "93d33b87-51f3-4c9c-a708-21f84f313759", "fields": {"created_on": "2023-06-06T15:39:49.248Z", "updated_on": "2023-06-06T16:23:36.844Z", "export": "3d29070f-c9bf-472b-b1ca-b53f82f4adf4", "name": "hp elitebook 6470p", "quantity": 10, "price_currency": "USD", "price": "750.00", "total_price_currency": "USD", "total_price": "7500.00", "weight": 0.84}}, {"model": "exports.item", "pk": "c4b36af0-6093-40da-81ed-68f1ab93e825", "fields": {"created_on": "2023-06-06T15:37:17.360Z", "updated_on": "2023-06-06T23:04:58.647Z", "export": "ca3f49f2-cc6b-4188-886b-914d7be6b8ee", "name": "iphone 11 promax", "quantity": 6, "price_currency": "USD", "price": "1235.00", "total_price_currency": "USD", "total_price": "7410.00", "weight": 1.0}}, {"model": "exports.item", "pk": "ed5ff640-effc-4a05-b43f-ec6995e3568b", "fields": {"created_on": "2023-06-06T15:36:21.179Z", "updated_on": "2023-06-06T23:05:04.517Z", "export": "ca3f49f2-cc6b-4188-886b-914d7be6b8ee", "name": "iphone x", "quantity": 5, "price_currency": "USD", "price": "1050.00", "total_price_currency": "USD", "total_price": "5250.00", "weight": 1.0}}, {"model": "imports.import", "pk": "25c2db61-d4f6-42fc-800c-baa3cde99ac5", "fields": {"created_on": "2023-06-06T22:38:51.875Z", "updated_on": "2023-06-29T20:28:32.285Z", "customer": "8ac17233-4cc1-4eb6-8a32-5e58c135fa47", "sender": "096c09dc-fecc-4a11-ba8c-0d8ab2be7ae7", "receiver_name": "mark zu<PERSON>", "receiver_address": "32 silicon valley road", "receiver_city": "silicon valley", "receiver_state": "washington", "receiver_country": "USA", "phone_no": "+14486574830", "weight": "3.00", "weight_unit": "kg", "lenght": 0.0, "breadth": 0.0, "height": 0.0, "volumentary_weight": 0.0, "volumentary_weight_unit": "kg", "import_fee": "76320.00", "tracking_no": "IMP1574110003", "payment_status": "unpaid", "payment_reference": null, "shipped_at": null, "shipping_status": "packaged & ready to ship", "attention": "", "email_sent": false}}, {"model": "imports.import", "pk": "495bc7b1-f1c5-4b2a-9775-268be4ab19ad", "fields": {"created_on": "2023-06-06T22:42:17.952Z", "updated_on": "2023-06-29T20:28:32.261Z", "customer": "8ac17233-4cc1-4eb6-8a32-5e58c135fa47", "sender": "e4acff5c-eee5-403b-88a9-ed269af98f1b", "receiver_name": "ada jesus", "receiver_address": "43 ugwulangbi street", "receiver_city": "<PERSON><PERSON><PERSON>", "receiver_state": "abia", "receiver_country": "Nigeria", "phone_no": "+2348176473645", "weight": "20.00", "weight_unit": "kg", "lenght": 0.0, "breadth": 0.0, "height": 0.0, "volumentary_weight": 0.0, "volumentary_weight_unit": "kg", "import_fee": "230550.00", "tracking_no": "IMP1638583893", "payment_status": "unpaid", "payment_reference": "603765580", "shipped_at": null, "shipping_status": "packaged & ready to ship", "attention": "", "email_sent": false}}, {"model": "imports.import", "pk": "c11255fe-ef08-425b-9597-b9a7df38a03c", "fields": {"created_on": "2023-06-06T22:34:43.876Z", "updated_on": "2023-08-23T15:44:34.141Z", "customer": "08878e09-2a2c-4983-8267-fa9c507d1447", "sender": "e4acff5c-eee5-403b-88a9-ed269af98f1b", "receiver_name": "james belushi", "receiver_address": "43 washington dc street", "receiver_city": "washington dc", "receiver_state": "washington", "receiver_country": "USA", "phone_no": "+14486574830", "weight": null, "weight_unit": "kg", "lenght": 0.0, "breadth": 0.0, "height": 0.0, "volumentary_weight": 0.0, "volumentary_weight_unit": "kg", "import_fee": "50540.00", "tracking_no": "IMP1584660310", "payment_status": "paid", "payment_reference": "572424990", "shipped_at": null, "shipping_status": "shipment  held by UK custom please provide purchase receipt", "attention": "There is an urgent need to upload documents for the items on this package.", "email_sent": true}}, {"model": "addresses.address", "pk": "096c09dc-fecc-4a11-ba8c-0d8ab2be7ae7", "fields": {"created_on": "2023-06-06T15:20:00.495Z", "updated_on": "2023-06-06T15:20:00.495Z", "name": "mog dynamics", "address": "34 second boulevard street", "city": "canada city", "state": "<PERSON><PERSON><PERSON>", "country": "Canada", "phone_number": "+14842329393"}}, {"model": "addresses.address", "pk": "0e958174-547e-4b21-a582-e13e61e4296e", "fields": {"created_on": "2023-06-06T15:16:26.804Z", "updated_on": "2023-06-06T15:16:26.804Z", "name": "mog dynamics", "address": "24 abike dabi street", "city": "igando", "state": "lagos", "country": "Nigeria", "phone_number": "+2348148783519"}}, {"model": "addresses.address", "pk": "e4acff5c-eee5-403b-88a9-ed269af98f1b", "fields": {"created_on": "2023-06-06T15:18:43.614Z", "updated_on": "2023-06-06T15:18:43.614Z", "name": "mog dynamics", "address": "100/102 silicon valley", "city": "california", "state": "california", "country": "USA", "phone_number": "+14842229993"}}, {"model": "logistics.logistic", "pk": "1168a77c-a0fd-42b9-a19d-f7c5282beb7d", "fields": {"created_on": "2023-08-25T15:09:48.636Z", "updated_on": "2023-08-25T15:34:14.249Z", "company": "9c153740-02d7-46a1-8e02-226541dcb618", "sender": "0e958174-547e-4b21-a582-e13e61e4296e", "receiver_name": "ada jesus", "receiver_address": "43 ugwulangbi street", "receiver_city": "<PERSON><PERSON><PERSON>", "receiver_state": "abia", "phone_no": "+2348158505959", "weight": null, "weight_unit": "kg", "lenght": 0.0, "breadth": 0.0, "height": 0.0, "volumentary_weight": 0.0, "volumentary_weight_unit": "kg", "delivery_fee": "456728.40", "tracking_no": "LCL1858943500", "payment_status": "unpaid", "payment_reference": null, "shipped_at": null, "delivery_status": "pending", "attention": "", "email_sent": false, "dispatcher": "5dad5182-2e2d-482e-b96e-ac6012e59e75"}}]