{% extends "base.html" %}
{% load humanize %}

{% block title %}Consolidated Items Management{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
    }
    
    .stat-item {
        background: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 8px;
        text-align: center;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        display: block;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .filter-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .table-responsive {
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-radius: 8px;
        overflow: hidden;
    }
    
    .item-type-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .export-badge {
        background-color: #28a745;
        color: white;
    }
    
    .import-badge {
        background-color: #007bff;
        color: white;
    }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
    }
    
    .btn-sync {
        background: linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%);
        border: none;
        color: white;
    }
    
    .btn-sync:hover {
        background: linear-gradient(45deg, #FE6B8B 60%, #FF8E53 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Consolidated Items Management</h1>
                <div>
                    <button class="btn btn-sync me-2" onclick="syncItems()">
                        <i class="fas fa-sync-alt"></i> Sync Items
                    </button>
                    <a href="{% url 'items:dashboard_stats' %}" class="btn btn-info">
                        <i class="fas fa-chart-bar"></i> Dashboard
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-card">
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.total_items }}</span>
                        <span class="stat-label">Total Items</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.export_items }}</span>
                        <span class="stat-label">Export Items</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.import_items }}</span>
                        <span class="stat-label">Import Items</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.pending_items }}</span>
                        <span class="stat-label">Pending</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.shipped_items }}</span>
                        <span class="stat-label">Shipped</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.unpaid_items }}</span>
                        <span class="stat-label">Unpaid</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.paid_items }}</span>
                        <span class="stat-label">Paid</span>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filter-section">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_query }}" placeholder="Search items, tracking no, customer...">
                    </div>
                    <div class="col-md-2">
                        <label for="item_type" class="form-label">Item Type</label>
                        <select class="form-select" id="item_type" name="item_type">
                            <option value="">All Types</option>
                            {% for value, label in item_type_choices %}
                                <option value="{{ value }}" {% if current_item_type == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="shipping_status" class="form-label">Shipping Status</label>
                        <select class="form-select" id="shipping_status" name="shipping_status">
                            <option value="">All Status</option>
                            {% for value, label in shipping_status_choices %}
                                <option value="{{ value }}" {% if current_shipping_status == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="payment_status" class="form-label">Payment Status</label>
                        <select class="form-select" id="payment_status" name="payment_status">
                            <option value="">All Status</option>
                            {% for value, label in payment_status_choices %}
                                <option value="{{ value }}" {% if current_payment_status == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="order_by" class="form-label">Order By</label>
                        <select class="form-select" id="order_by" name="order_by">
                            <option value="-created_on" {% if current_order_by == '-created_on' %}selected{% endif %}>
                                Newest First
                            </option>
                            <option value="created_on" {% if current_order_by == 'created_on' %}selected{% endif %}>
                                Oldest First
                            </option>
                            <option value="name" {% if current_order_by == 'name' %}selected{% endif %}>
                                Name A-Z
                            </option>
                            <option value="-name" {% if current_order_by == '-name' %}selected{% endif %}>
                                Name Z-A
                            </option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block w-100">Filter</button>
                    </div>
                </form>
            </div>

            <!-- Bulk Actions -->
            <div class="mb-3">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-success" onclick="bulkUpdateStatus('shipping', 'Shipped')">
                        Mark as Shipped
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="bulkUpdateStatus('payment', 'Paid')">
                        Mark as Paid
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="bulkUpdateStatus('shipping', 'Pending')">
                        Mark as Pending
                    </button>
                </div>
                <small class="text-muted ms-2">Select items below to perform bulk actions</small>
            </div>

            <!-- Items Table -->
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th><input type="checkbox" id="selectAll"></th>
                            <th>Item Name</th>
                            <th>Type</th>
                            <th>Customer</th>
                            <th>Tracking No</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Total</th>
                            <th>Weight</th>
                            <th>Shipping Status</th>
                            <th>Payment Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in page_obj %}
                        <tr>
                            <td><input type="checkbox" class="item-checkbox" value="{{ item.id }}"></td>
                            <td>
                                <strong>{{ item.name }}</strong>
                            </td>
                            <td>
                                <span class="item-type-badge {% if item.item_type == 'export' %}export-badge{% else %}import-badge{% endif %}">
                                    {{ item.get_item_type_display }}
                                </span>
                            </td>
                            <td>{{ item.customer_full_name }}</td>
                            <td>
                                <code>{{ item.tracking_no }}</code>
                            </td>
                            <td>{{ item.quantity }}</td>
                            <td>${{ item.price|floatformat:2 }}</td>
                            <td><strong>${{ item.total_price|floatformat:2 }}</strong></td>
                            <td>{{ item.weight }} kg</td>
                            <td>
                                <span class="badge bg-{% if item.shipping_status == 'Shipped' %}success{% elif item.shipping_status == 'Pending' %}warning{% else %}secondary{% endif %}">
                                    {{ item.shipping_status }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{% if item.payment_status == 'Paid' %}success{% else %}danger{% endif %}">
                                    {{ item.payment_status }}
                                </span>
                            </td>
                            <td>{{ item.created_on|date:"M d, Y" }}</td>
                            <td>
                                <a href="{% url 'items:consolidated_item_detail' item.id %}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ item.get_absolute_url }}" 
                                   class="btn btn-sm btn-outline-info" target="_blank">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="13" class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No items found matching your criteria.</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Items pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_item_type %}&item_type={{ current_item_type }}{% endif %}{% if current_shipping_status %}&shipping_status={{ current_shipping_status }}{% endif %}{% if current_payment_status %}&payment_status={{ current_payment_status }}{% endif %}&order_by={{ current_order_by }}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_item_type %}&item_type={{ current_item_type }}{% endif %}{% if current_shipping_status %}&shipping_status={{ current_shipping_status }}{% endif %}{% if current_payment_status %}&payment_status={{ current_payment_status }}{% endif %}&order_by={{ current_order_by }}">Previous</a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_item_type %}&item_type={{ current_item_type }}{% endif %}{% if current_shipping_status %}&shipping_status={{ current_shipping_status }}{% endif %}{% if current_payment_status %}&payment_status={{ current_payment_status }}{% endif %}&order_by={{ current_order_by }}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_item_type %}&item_type={{ current_item_type }}{% endif %}{% if current_shipping_status %}&shipping_status={{ current_shipping_status }}{% endif %}{% if current_payment_status %}&payment_status={{ current_payment_status }}{% endif %}&order_by={{ current_order_by }}">Last</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Sync items function
function syncItems() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';
    btn.disabled = true;
    
    fetch('{% url "items:sync_items" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token }}',
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error syncing items: ' + error);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// Bulk update status function
function bulkUpdateStatus(updateType, newStatus) {
    const selectedItems = Array.from(document.querySelectorAll('.item-checkbox:checked')).map(cb => cb.value);
    
    if (selectedItems.length === 0) {
        alert('Please select at least one item.');
        return;
    }
    
    if (!confirm(`Are you sure you want to update ${selectedItems.length} items?`)) {
        return;
    }
    
    const formData = new FormData();
    selectedItems.forEach(id => formData.append('item_ids', id));
    formData.append('update_type', updateType);
    formData.append('new_status', newStatus);
    formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
    
    fetch('{% url "items:bulk_update_status" %}', {
        method: 'POST',
        body: formData,
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error updating items: ' + error);
    });
}
</script>
{% endblock %}