﻿{% extends 'base.html' %}
{% load static %}


{% block content %}
<!-- =-=-=-=-=-=-= Sign In Section =-=-=-=-=-=-= -->

<section class="section-padding-70">
	<div class="container">
		<div class="row">
			<div class="col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2">
				<form class="registration" action="{% url 'user_login' %}" method="post">
					{% csrf_token %}
					<div class="box-header">
						<h2>Login to your account</h2>
					</div>

					<label for="email">Email Address <span class="color-red">*</span></label>
					<input type="email" class="form-control margin-bottom-20" name="email" id="email" required>

					<label for="password">Password <span class="color-red">*</span></label>
					<input type="password" class="form-control margin-bottom-20" name="password" id="password" required>

					{% if request.GET.next %}
					<input type="hidden" name="next" value="{{request.GET.next}}">
					{% endif %}

					<div class="row">
						<div class="col-md-8 col-sm-8 col-xs-12">
							<div class="checkbox checkbox-default">
								<input type="checkbox" id="checkbox2" checked="">
								<label for="checkbox2">
									Stay signed in
								</label>
							</div>
						</div>
						<div class="col-md-4 row col-sm-4 col-xs-12 text-right">
							<button type="submit" class="btn btn-primary btn-sm">Login</button>
						</div>
					</div>
					<hr>
					<h4>Forget your Password ?</h4>
					<p>no worries, <a href="{% url 'custom_password_reset' %}" class="color-green">click here</a> to
						reset your password.</p>
				</form>
			</div>
		</div>
	</div>
</section>

<!-- =-=-=-=-=-=-= Section End =-=-=-=-=-=-= -->
{% endblock content %}