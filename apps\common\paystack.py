import requests
from core.settings import base
from django.db import transaction
from django.contrib import messages
from apps.exports.models import Export
from django.shortcuts import redirect, resolve_url
from django.contrib.auth.decorators import login_required
from apps.common.generator import generate_logistics_payment_reference_number


@login_required(login_url="user_login")
@transaction.atomic()
def initiate_payment(request, export_obj):
    user = request.user

    # paystack_live_secret_key = str(base.PAYSTACK_LIVE_SECRET_KEY)
    paystack_test_secret_key = str(base.PAYSTACK_TEST_SECRET_KEY)

    email = user.email
    phone_no = user.phone_no
    amount = float(export_obj.export_fee * 100)
    reference = generate_logistics_payment_reference_number()
    scheme = request.scheme
    domain = request.get_host()
    callback_url = f"{scheme}://{domain}{resolve_url('verify_export_payment')}"

    # Set the request headers
    headers = {
        "Authorization": f"Bearer {paystack_test_secret_key}",
        "Content-Type": "application/json"
    }

    data = {
        "amount": amount,
        "email": email,
        "callback_url": callback_url,
        "reference": reference,
        "customer": {
            "first_name": user.first_name,
            "last_name": user.last_name,
            "email": email,
            "phone": str(phone_no)
        },
        "channels": ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer'],
    }

    paystack_url = "https://api.paystack.co/transaction/initialize"
    response = requests.post(paystack_url, headers=headers, json=data, timeout=30)
    response_data = response.json()['data']
    authorization_link = response_data['authorization_url']
    transaction_reference = response_data["reference"]
    Export.objects.filter(id=export_obj.id).update(
        payment_reference=transaction_reference
    )
    return redirect(authorization_link)
