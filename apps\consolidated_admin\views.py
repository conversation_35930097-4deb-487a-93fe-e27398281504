from django.shortcuts import get_object_or_404
from django.views.generic import ListView, DetailView, TemplateView
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.contrib import messages
from django.db.models import Q, Count, Sum
from .models import ConsolidatedShipment, ConsolidatedItem, ShipmentType
from apps.imports.models import Import, Item as ImportItem
from apps.exports.models import Export, Item as ExportItem


@method_decorator(staff_member_required, name='dispatch')
class ConsolidatedShipmentListView(ListView):
    model = ConsolidatedShipment
    template_name = 'consolidated_admin/shipment_list.html'
    context_object_name = 'shipments'
    paginate_by = 25
    
    def get_queryset(self):
        queryset = ConsolidatedShipment.objects.select_related(
            'customer', 'sender', 'destination'
        ).prefetch_related('items')
        
        # Filter by shipment type
        shipment_type = self.request.GET.get('type')
        if shipment_type in ['import', 'export']:
            queryset = queryset.filter(shipment_type=shipment_type)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(shipping_status=status)
        
        # Filter by payment status
        payment_status = self.request.GET.get('payment')
        if payment_status:
            queryset = queryset.filter(payment_status=payment_status)
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(tracking_no__icontains=search) |
                Q(customer__first_name__icontains=search) |
                Q(customer__last_name__icontains=search) |
                Q(customer__email__icontains=search) |
                Q(payment_reference__icontains=search)
            )
        
        return queryset.order_by('-created_on')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['shipment_types'] = ShipmentType.choices
        context['current_type'] = self.request.GET.get('type', '')
        context['current_status'] = self.request.GET.get('status', '')
        context['current_payment'] = self.request.GET.get('payment', '')
        context['search_query'] = self.request.GET.get('search', '')
        return context


@method_decorator(staff_member_required, name='dispatch')
class ConsolidatedShipmentDetailView(DetailView):
    model = ConsolidatedShipment
    template_name = 'consolidated_admin/shipment_detail.html'
    context_object_name = 'shipment'
    
    def get_object(self):
        return get_object_or_404(
            ConsolidatedShipment.objects.select_related(
                'customer', 'sender', 'destination', 'import_reference', 'export_reference'
            ).prefetch_related('items'),
            pk=self.kwargs['pk']
        )


@method_decorator(staff_member_required, name='dispatch')
class ConsolidatedItemListView(ListView):
    model = ConsolidatedItem
    template_name = 'consolidated_admin/item_list.html'
    context_object_name = 'items'
    paginate_by = 50
    
    def get_queryset(self):
        queryset = ConsolidatedItem.objects.select_related(
            'shipment', 'shipment__customer'
        )
        
        # Filter by shipment type
        shipment_type = self.request.GET.get('type')
        if shipment_type in ['import', 'export']:
            queryset = queryset.filter(shipment__shipment_type=shipment_type)
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(shipment__tracking_no__icontains=search) |
                Q(shipment__customer__first_name__icontains=search) |
                Q(shipment__customer__last_name__icontains=search)
            )
        
        return queryset.order_by('-created_on')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['shipment_types'] = ShipmentType.choices
        context['current_type'] = self.request.GET.get('type', '')
        context['search_query'] = self.request.GET.get('search', '')
        return context


@method_decorator(staff_member_required, name='dispatch')
class ConsolidatedItemDetailView(DetailView):
    model = ConsolidatedItem
    template_name = 'consolidated_admin/item_detail.html'
    context_object_name = 'item'
    
    def get_object(self):
        return get_object_or_404(
            ConsolidatedItem.objects.select_related(
                'shipment', 'shipment__customer', 'import_item_reference', 'export_item_reference'
            ),
            pk=self.kwargs['pk']
        )


@method_decorator(staff_member_required, name='dispatch')
class ConsolidatedDashboardView(TemplateView):
    template_name = 'consolidated_admin/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Overall statistics
        context['total_shipments'] = ConsolidatedShipment.objects.count()
        context['total_imports'] = ConsolidatedShipment.objects.filter(
            shipment_type=ShipmentType.IMPORT
        ).count()
        context['total_exports'] = ConsolidatedShipment.objects.filter(
            shipment_type=ShipmentType.EXPORT
        ).count()
        context['total_items'] = ConsolidatedItem.objects.count()
        
        # Status breakdown
        context['status_breakdown'] = ConsolidatedShipment.objects.values(
            'shipping_status'
        ).annotate(count=Count('id')).order_by('-count')
        
        # Payment status breakdown
        context['payment_breakdown'] = ConsolidatedShipment.objects.values(
            'payment_status'
        ).annotate(count=Count('id')).order_by('-count')
        
        # Recent shipments
        context['recent_shipments'] = ConsolidatedShipment.objects.select_related(
            'customer'
        ).order_by('-created_on')[:10]
        
        # Revenue statistics (if needed)
        context['total_revenue'] = ConsolidatedShipment.objects.filter(
            payment_status='Paid'
        ).aggregate(total=Sum('shipping_fee'))['total'] or 0
        
        return context


@method_decorator(staff_member_required, name='dispatch')
class SyncDataView(TemplateView):
    template_name = 'consolidated_admin/sync_data.html'
    
    def post(self, request, *args, **kwargs):
        sync_type = request.POST.get('sync_type')
        
        if sync_type == 'imports':
            synced_count = self.sync_imports()
            messages.success(request, f'Successfully synced {synced_count} import records.')
        elif sync_type == 'exports':
            synced_count = self.sync_exports()
            messages.success(request, f'Successfully synced {synced_count} export records.')
        elif sync_type == 'both':
            import_count = self.sync_imports()
            export_count = self.sync_exports()
            messages.success(
                request, 
                f'Successfully synced {import_count} imports and {export_count} exports.'
            )
        else:
            messages.error(request, 'Invalid sync type.')
        
        return self.get(request, *args, **kwargs)
    
    def sync_imports(self):
        imports = Import.objects.filter(
            Q(consolidatedshipment__isnull=True) | 
            Q(consolidatedshipment__import_reference__isnull=True)
        )
        
        synced_count = 0
        for import_obj in imports:
            consolidated, created = ConsolidatedShipment.objects.get_or_create(
                import_reference=import_obj,
                defaults={
                    'shipment_type': ShipmentType.IMPORT,
                    'customer': import_obj.customer,
                    'sender': import_obj.sender,
                    'destination': import_obj.destination,
                    'phone_no': import_obj.phone_no,
                    'weight': import_obj.weight,
                    'weight_unit': import_obj.weight_unit,
                    'length': import_obj.lenght,
                    'breadth': import_obj.breadth,
                    'height': import_obj.height,
                    'volumetric_weight': import_obj.volumentary_weight,
                    'volumetric_weight_unit': import_obj.volumentary_weight_unit,
                    'shipping_fee': import_obj.import_fee,
                    'tracking_no': import_obj.tracking_no,
                    'payment_status': import_obj.payment_status,
                    'payment_reference': import_obj.payment_reference,
                    'shipped_at': import_obj.shipped_at,
                    'shipping_status': import_obj.shipping_status,
                    'attention': import_obj.attention,
                    'email_sent': import_obj.email_sent,
                }
            )
            
            if created:
                synced_count += 1
                # Sync items
                for item in ImportItem.objects.filter(imports=import_obj):
                    ConsolidatedItem.objects.get_or_create(
                        import_item_reference=item,
                        defaults={
                            'shipment': consolidated,
                            'name': item.name,
                            'quantity': item.quantity,
                            'price': item.price,
                            'total_price': item.total_price,
                            'weight': item.weight,
                        }
                    )
        
        return synced_count
    
    def sync_exports(self):
        exports = Export.objects.filter(
            Q(consolidatedshipment__isnull=True) | 
            Q(consolidatedshipment__export_reference__isnull=True)
        )
        
        synced_count = 0
        for export_obj in exports:
            consolidated, created = ConsolidatedShipment.objects.get_or_create(
                export_reference=export_obj,
                defaults={
                    'shipment_type': ShipmentType.EXPORT,
                    'customer': export_obj.customer,
                    'sender': export_obj.sender,
                    'destination': export_obj.destination,
                    'phone_no': export_obj.phone_no,
                    'weight': export_obj.weight,
                    'weight_unit': export_obj.weight_unit,
                    'length': export_obj.lenght,
                    'breadth': export_obj.breadth,
                    'height': export_obj.height,
                    'volumetric_weight': export_obj.volumentary_weight,
                    'volumetric_weight_unit': export_obj.volumentary_weight_unit,
                    'shipping_fee': export_obj.export_fee,
                    'tracking_no': export_obj.tracking_no,
                    'payment_status': export_obj.payment_status,
                    'payment_reference': export_obj.payment_reference,
                    'shipped_at': export_obj.shipped_at,
                    'shipping_status': export_obj.shipping_status,
                    'tracking_url': export_obj.tracking_url,
                    'attention': export_obj.attention,
                    'email_sent': export_obj.email_sent,
                }
            )
            
            if created:
                synced_count += 1
                # Sync items
                for item in ExportItem.objects.filter(export=export_obj):
                    ConsolidatedItem.objects.get_or_create(
                        export_item_reference=item,
                        defaults={
                            'shipment': consolidated,
                            'name': item.name,
                            'quantity': item.quantity,
                            'price': item.price,
                            'total_price': item.total_price,
                            'weight': item.weight,
                        }
                    )
        
        return synced_count
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['import_count'] = Import.objects.count()
        context['export_count'] = Export.objects.count()
        context['consolidated_count'] = ConsolidatedShipment.objects.count()
        context['unsynced_imports'] = Import.objects.filter(
            consolidatedshipment__isnull=True
        ).count()
        context['unsynced_exports'] = Export.objects.filter(
            consolidatedshipment__isnull=True
        ).count()
        return context
