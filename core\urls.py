from .import views
from django.contrib import admin
from django.conf import settings
from django.urls import path, include
from django.conf.urls.static import static


urlpatterns = [
    path('admin/', admin.site.urls),
    path('', views.home_view, name="home"),
    path('about', views.about_view, name="about"),
    path('services', views.service_view, name="services"),
    path('processes', views.process_view, name="processes"),
    path('our-team', views.our_team_view, name="our_team"),
    path('cargo-booking', views.cargo_booking_view, name="cargo_booking"),
    path('contact-us', views.contact_view, name="contact_us"),

    # API endpoints
    path('api/v1/', include("apps.users.api_urls")),
    path('api/v1/', include("apps.pricing.api_urls")),
    path('api/v1/', include("apps.imports.api_urls")),
    path('api/v1/', include("apps.logistics.api_urls")),
    path('api/v1/', include("apps.addresses.api_urls")),
    path('api/v1/', include("apps.exports.api_urls")),
    path('api/v1/', include("apps.company.api_urls")),
    path('api/v1/', include("apps.galleries.api_urls")),
    path('api/v1/', include("apps.common.api_urls")),

    # Web endpoints
    path('pricing/', include("apps.pricing.urls")),
    path('galleries/', include("apps.galleries.urls")),
    path('users/', include("apps.users.urls")),
    path('exports/', include("apps.exports.urls")),
    path('imports/', include("apps.imports.urls")),
    path('logistics/', include("apps.logistics.urls")),
    path('company/', include("apps.company.urls")),
    # path('consolidated-admin/', include("apps.consolidated_admin.urls")),
    path('items/', include("apps.items.urls")),
]

handler404 = 'core.views.custom_page_404_not_found'
handler500 = 'core.views.custom_page_500_server_error'

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)


admin.site.site_header = "Mog Dynamics Administration"
admin.site.site_title = "Mog Dynamics Logistics"
admin.site.index_title = "Mog Dynamics Logistics"
