{% extends 'dashboard.html' %}
{% load static %}


{% block content %}
  <!--main-->
  <main role="main" class="container main">
    <h3 class="color-black">My Profile</h3>
    <p>
        <a class="link" href="{% url 'user_profile' %}">Home</a> | 
        <a class="link" href="{% url 'user_profile' %}">Profile</a></h1> |
        view
    </p>
    <hr>
    <div class="row cards justify-content-center mb-2">
      <div class="rounded col-12 col-sm-6 col-md-6 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            {% if user.profile_picture %}
              <img class="profile-picture" src="{{user.profile_picture.url}}" alt="profile image">
            {% else %}
              <img class="profile-picture" src="{% static './media/images/dummy_qr.png' %}" alt="profile image">
            {% endif %}
            
            {% if user.user_type == "small business" %}
              <h3 class="card-title">{{user.get_business_name}}</h3>
            {% else %}
               <h3 class="card-title">{{user.get_full_name}}</h3>
            {% endif %}
            <hr>
            <p>{{user.email}}</p>
            <hr>
            <p>{{ user.phone_no }}</p>
            <hr>
            <a class="btn btn-warning mb-2 w-50" href="{% url 'update_profile' user.id %}">Edit Profile</a>
            <hr>
          </div>
        </div>
      </div>

      <div class="rounded col-12 col-sm-6 col-md-6 mb-3">
        <div class="card">
          <div class="card-body">
            <h4>Your shopping address</h4>
            <hr>

            <small>Warehouse Options:</small>      
            <select name="address"
                    id="filter-address"
                    class="form-control"
                    hx-get="{% url 'company_address' %}"
                    hx-trigger="change"
                    hx-target="#filter-result">
              {% for address in address_list %}
                <option value="{{ address.address }}"> {{ address.address }} {{ address.state }} {{ address.country }}</option>
              {% endfor %}
            </select><br>

            <div id="filter-result">
              {% include "partial/company_address.html" %}
            </div>
            <br>
            <br>
            <br>
          </div>
        </div>
      </div>

      {% if user.user_type == "small business" %}
        <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
          <div class="card">
            <div class="card-body">
              <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-cart-check-fill text-success" viewBox="0 0 16 16">
                <path d="M.5 1a.5.5 0 0 0 0 1h1.11l.401 1.607 1.498 7.985A.5.5 0 0 0 4 12h1a2 2 0 1 0 0 4 2 2 0 0 0 0-4h7a2 2 0 1 0 0 4 2 2 0 0 0 0-4h1a.5.5 0 0 0 .491-.408l1.5-8A.5.5 0 0 0 14.5 3H2.89l-.405-1.621A.5.5 0 0 0 2 1H.5zM6 14a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm7 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm-1.646-7.646-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L8 8.293l2.646-2.647a.5.5 0 0 1 .708.708z"/>
              </svg>
              <h4 class="card-title">Total Imports</h4>
              <p class="card-text">{{ total_imports }}</p>
            </div>
          </div>
        </div>

        <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
          <div class="card">
            <div class="card-body">
              <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-check-all text-warning" viewBox="0 0 16 16">
                <path d="M8.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L2.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093L8.95 4.992a.252.252 0 0 1 .02-.022zm-.92 5.14.92.92a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 1 0-1.091-1.028L9.477 9.417l-.485-.486-.943 1.179z"/>
              </svg>
              <h4 class="card-title">Paid Imports</h4>
              <p class="card-text">{{ paid_imports }}</p>
            </div>
          </div>
        </div>

        <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
          <div class="card">
            <div class="card-body">
              <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-bookmark-x-fill text-danger" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M2 15.5V2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.74.439L8 13.069l-5.26 2.87A.5.5 0 0 1 2 15.5zM6.854 5.146a.5.5 0 1 0-.708.708L7.293 7 6.146 8.146a.5.5 0 1 0 .708.708L8 7.707l1.146 1.147a.5.5 0 1 0 .708-.708L8.707 7l1.147-1.146a.5.5 0 0 0-.708-.708L8 6.293 6.854 5.146z"/>
              </svg>
              <h4 class="card-title">Unpaid Imports</h4>
              <p class="card-text">{{ unpaid_imports }}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-cart-check-fill text-success" viewBox="0 0 16 16">
              <path d="M.5 1a.5.5 0 0 0 0 1h1.11l.401 1.607 1.498 7.985A.5.5 0 0 0 4 12h1a2 2 0 1 0 0 4 2 2 0 0 0 0-4h7a2 2 0 1 0 0 4 2 2 0 0 0 0-4h1a.5.5 0 0 0 .491-.408l1.5-8A.5.5 0 0 0 14.5 3H2.89l-.405-1.621A.5.5 0 0 0 2 1H.5zM6 14a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm7 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm-1.646-7.646-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L8 8.293l2.646-2.647a.5.5 0 0 1 .708.708z"/>
            </svg>
            <h4 class="card-title">Total Exports</h4>
            <p class="card-text">{{ total_exports }}</p>
          </div>
        </div>
      </div>

      <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-check-all text-warning" viewBox="0 0 16 16">
              <path d="M8.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L2.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093L8.95 4.992a.252.252 0 0 1 .02-.022zm-.92 5.14.92.92a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 1 0-1.091-1.028L9.477 9.417l-.485-.486-.943 1.179z"/>
            </svg>
            <h4 class="card-title">Paid Exports</h4>
            <p class="card-text">{{ paid_exports }}</p>
          </div>
        </div>
      </div>

      <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-bookmark-x-fill text-danger" viewBox="0 0 16 16">
              <path fill-rule="evenodd" d="M2 15.5V2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.74.439L8 13.069l-5.26 2.87A.5.5 0 0 1 2 15.5zM6.854 5.146a.5.5 0 1 0-.708.708L7.293 7 6.146 8.146a.5.5 0 1 0 .708.708L8 7.707l1.146 1.147a.5.5 0 1 0 .708-.708L8.707 7l1.147-1.146a.5.5 0 0 0-.708-.708L8 6.293 6.854 5.146z"/>
            </svg>
            <h4 class="card-title">Unpaid Exports</h4>
            <p class="card-text">{{ unpaid_exports }}</p>
          </div>
        </div>
      </div>

      <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-cart-check-fill text-success" viewBox="0 0 16 16">
              <path d="M.5 1a.5.5 0 0 0 0 1h1.11l.401 1.607 1.498 7.985A.5.5 0 0 0 4 12h1a2 2 0 1 0 0 4 2 2 0 0 0 0-4h7a2 2 0 1 0 0 4 2 2 0 0 0 0-4h1a.5.5 0 0 0 .491-.408l1.5-8A.5.5 0 0 0 14.5 3H2.89l-.405-1.621A.5.5 0 0 0 2 1H.5zM6 14a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm7 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm-1.646-7.646-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L8 8.293l2.646-2.647a.5.5 0 0 1 .708.708z"/>
            </svg>
            <h4 class="card-title">Total Local Deliveries</h4>
            <p class="card-text">{{ total_orders }}</p>
          </div>
        </div>
      </div>

      <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-check-all text-warning" viewBox="0 0 16 16">
              <path d="M8.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L2.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093L8.95 4.992a.252.252 0 0 1 .02-.022zm-.92 5.14.92.92a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 1 0-1.091-1.028L9.477 9.417l-.485-.486-.943 1.179z"/>
            </svg>
            <h4 class="card-title">Paid Local Deliveries</h4>
            <p class="card-text">{{ paid_orders }}</p>
          </div>
        </div>
      </div>

      <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-bookmark-x-fill text-danger" viewBox="0 0 16 16">
              <path fill-rule="evenodd" d="M2 15.5V2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.74.439L8 13.069l-5.26 2.87A.5.5 0 0 1 2 15.5zM6.854 5.146a.5.5 0 1 0-.708.708L7.293 7 6.146 8.146a.5.5 0 1 0 .708.708L8 7.707l1.146 1.147a.5.5 0 1 0 .708-.708L8.707 7l1.147-1.146a.5.5 0 0 0-.708-.708L8 6.293 6.854 5.146z"/>
            </svg>
            <h4 class="card-title">Unpaid Local Deliveries</h4>
            <p class="card-text">{{ unpaid_orders }}</p>
          </div>
        </div>
      </div>
    {% else %}
      <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-cart-check-fill text-success" viewBox="0 0 16 16">
              <path d="M.5 1a.5.5 0 0 0 0 1h1.11l.401 1.607 1.498 7.985A.5.5 0 0 0 4 12h1a2 2 0 1 0 0 4 2 2 0 0 0 0-4h7a2 2 0 1 0 0 4 2 2 0 0 0 0-4h1a.5.5 0 0 0 .491-.408l1.5-8A.5.5 0 0 0 14.5 3H2.89l-.405-1.621A.5.5 0 0 0 2 1H.5zM6 14a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm7 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm-1.646-7.646-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L8 8.293l2.646-2.647a.5.5 0 0 1 .708.708z"/>
            </svg>
            <h4 class="card-title">Total Exports</h4>
            <p class="card-text">{{ total_exports }}</p>
          </div>
        </div>
      </div>

      <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-check-all text-warning" viewBox="0 0 16 16">
              <path d="M8.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L2.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093L8.95 4.992a.252.252 0 0 1 .02-.022zm-.92 5.14.92.92a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 1 0-1.091-1.028L9.477 9.417l-.485-.486-.943 1.179z"/>
            </svg>
            <h4 class="card-title">Paid Exports</h4>
            <p class="card-text">{{ paid_exports }}</p>
          </div>
        </div>
      </div>

      <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-bookmark-x-fill text-danger" viewBox="0 0 16 16">
              <path fill-rule="evenodd" d="M2 15.5V2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.74.439L8 13.069l-5.26 2.87A.5.5 0 0 1 2 15.5zM6.854 5.146a.5.5 0 1 0-.708.708L7.293 7 6.146 8.146a.5.5 0 1 0 .708.708L8 7.707l1.146 1.147a.5.5 0 1 0 .708-.708L8.707 7l1.147-1.146a.5.5 0 0 0-.708-.708L8 6.293 6.854 5.146z"/>
            </svg>
            <h4 class="card-title">Unpaid Exports</h4>
            <p class="card-text">{{ unpaid_exports }}</p>
          </div>
        </div>
      </div>

      <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-cart-check-fill text-success" viewBox="0 0 16 16">
              <path d="M.5 1a.5.5 0 0 0 0 1h1.11l.401 1.607 1.498 7.985A.5.5 0 0 0 4 12h1a2 2 0 1 0 0 4 2 2 0 0 0 0-4h7a2 2 0 1 0 0 4 2 2 0 0 0 0-4h1a.5.5 0 0 0 .491-.408l1.5-8A.5.5 0 0 0 14.5 3H2.89l-.405-1.621A.5.5 0 0 0 2 1H.5zM6 14a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm7 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm-1.646-7.646-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L8 8.293l2.646-2.647a.5.5 0 0 1 .708.708z"/>
            </svg>
            <h4 class="card-title">Total Imports</h4>
            <p class="card-text">{{ total_imports }}</p>
          </div>
        </div>
      </div>

      <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-check-all text-warning" viewBox="0 0 16 16">
              <path d="M8.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L2.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093L8.95 4.992a.252.252 0 0 1 .02-.022zm-.92 5.14.92.92a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 1 0-1.091-1.028L9.477 9.417l-.485-.486-.943 1.179z"/>
            </svg>
            <h4 class="card-title">Paid Imports</h4>
            <p class="card-text">{{ paid_imports }}</p>
          </div>
        </div>
      </div>

      <div class="rounded col-12 col-sm-4 col-md-4 mb-3 text-center">
        <div class="card">
          <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-bookmark-x-fill text-danger" viewBox="0 0 16 16">
              <path fill-rule="evenodd" d="M2 15.5V2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.74.439L8 13.069l-5.26 2.87A.5.5 0 0 1 2 15.5zM6.854 5.146a.5.5 0 1 0-.708.708L7.293 7 6.146 8.146a.5.5 0 1 0 .708.708L8 7.707l1.146 1.147a.5.5 0 1 0 .708-.708L8.707 7l1.147-1.146a.5.5 0 0 0-.708-.708L8 6.293 6.854 5.146z"/>
            </svg>
            <h4 class="card-title">Unpaid Imports</h4>
            <p class="card-text">{{ unpaid_imports }}</p>
          </div>
        </div>
      </div>
    {% endif %}
  </main>
{% endblock content %}
