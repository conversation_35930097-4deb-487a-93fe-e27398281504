{% extends 'base.html' %}
{% block title %}Delivery Information{% endblock title %}


{% block content %}
    <div class="container section-padding-100 gray">
        {% if result_obj %}
            <div class="row custom-justify-content-center cards">
                <div class="col-xs-10 col-sm-11 col-md-10 col-lg-8 cards">
                    <h3 class="text-center mb-1 text-warning">Delivery Details</h3>
                    <div class="card">
                        <h5 class="mt-3 text-info">{{ result_obj.tracking_no }}</h5>
                        <hr>
                        <div class="row cards">
                            <div class="col-12 col-sm-6 col-md-6 col-lg-6 card gray mt-1">
                                <div class="sender-detail">
                                    <h5 class="text-warning"><b>Tracking No:</b></h5>
                                    <p class="text-primary">{{ result_obj.tracking_no|capfirst}}</p>
                                </div>
                            </div>
        
                            <div class="col-12 col-sm-6 col-md-6 col-lg-6 card gray mt-1">
                                <div class="receiver-detail">
                                    <h5 class="text-warning"><b>Delivery Status:</b></h5>
                                    <p class="text-primary">{{ result_obj.delivery_status|capfirst }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="card col-12 col-sm-6 col-md-6 col-lg-6 gray mt-1">
                                <div class="p-2 other-details">
                                    <h5 class="text-warning"><b>Origin Warehouse:</b></h5>
                                    <p class="text-primary">{{ result_obj.sender.city|capfirst }}, {{ result_obj.sender.state|capfirst }}, {{ result_obj.sender.country|capfirst }}</p>
                                </div>
                            </div>
        
                            <div class="card col-12 col-sm-6 col-md-6 col-lg-6 gray mt-1">
                                <div class="p-2 other-details">
                                    <h5 class="text-warning"><b>Destination:</b></h5>
                                    <p class="text-primary">
                                        {{ result_obj.sender.address|capfirst}},
                                        {{ result_obj.sender.city }},
                                        {{ result_obj.sender.statte }}
                                        {{ result_obj.sender.country }}
                                    </p>
                                </div>
                            </div>
        
                            {% if result_obj.attention %}
                                <div class="card col-xs-12 col-sm-7 col-md-7 col-lg-7 gray mt-1">
                                    <div class="p-2 other-details">
                                        <p style="float: right;"><small>{{ result_obj.updated_on }}</small></p>
                                        <p class="text-primary"><b>{{ result_obj.delivery_status|capfirst }}</b></p>
                                    </div>
                                </div>
                                <div class="card col-xs-12 col-sm-5 col-md-5 col-lg-5 gray mt-1">
                                    <div class="p-2 other-details">
                                        <h5>Attention!</h5>
                                        <p><small>{{ result_obj.attention|capfirst }}</small></p>
                                        <p style="float: right;"><small>{{ result_obj.updated_on }}</small></p>
                                    </div>
                                </div>
                            {% else %}
                                <div class="card col-xs-12 col-sm-12 col-md-12 col-lg-12 gray mt-1">
                                    <div class="p-2 other-details">
                                        <p style="float: right;"><small>{{ result_obj.updated_on }}</small></p>
                                        <p>Status</p>
                                        <p class="text-primary"><b>{{ result_obj.delivery_status|capfirst }}</b></p>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <a class="btn btn-info btn-sm mt-1 text-white" href="{% url 'track_local_order' %}">Track another delivery.</a>
                </div>
            </div>
        {% else %}
            <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 text-center" style="margin-top:-50px">
                <h2>No result found.</h2>
                <a class="btn btn-info btn-sm mt-1 text-white" href="{% url 'track_local_order' %}">Track another delivery.</a>
            </div>
        {% endif %}
    </div>
{% endblock content %}