﻿{% extends 'base.html' %} {% block content %}
<!-- =-=-=-=-=-=-= PAGE BREADCRUMB =-=-=-=-=-=-= -->
<section class="breadcrumbs-area parallex">
  <div class="container">
    <div class="row">
      <div class="page-title">
        <div class="col-sm-12 col-md-6 page-heading text-left">
          <h3>Our memories</h3>
          <h2>Gallery Page</h2>
        </div>
        <div class="col-sm-12 col-md-6 text-right">
          <ul class="breadcrumbs">
            <li><a href="{% url 'home' %}">home</a></li>
            <li><a href="{% url 'gallery' %}">gallery</a></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- =-=-=-=-=-=-= PAGE BREADCRUMB END =-=-=-=-=-=-= -->

<div class="row justify-content-center">
  <div class="col-12 cold-sm-12 col-md-12 col-lg-12">
    <div class="services-box-2">
      <h3 class="mt-4 text-center">
        Welcome to our logistics website gallery, where images speak volumes
        about our commitment to your success!
      </h3>
    </div>
  </div>
</div>
<!-- =-=-=-=-=-=-= Gallery =-=-=-=-=-=-= -->
<section id="gallery" class="custom-padding">
  <div class="container">
    <div class="portfolio-container text-center">
      {% if gallery_list %}
        <ul id="portfolio-grid" class="three-column hover-two">
          {% for gallery in gallery_list %}
          <li class="portfolio-item gutter">
            <div class="portfolio">
              <div class="tt-overlay"></div>
              <img src="{{ gallery.picture.url }}" alt="gallery images" />
              <!-- <fig>{{ gallery.title|capfirst }}</fig> -->
            </div>
          </li>
          {% endfor %}
        </ul>
      {% else %}
        <h1>Gallery images coming soon.</h1>
      {% endif %}
    </div>

    <!-- portfolio-container -->
  </div>
  <!-- end container -->
</section>
<!-- =-=-=-=-=-=-= Blog & News end =-=-=-=-=-=-= -->

<!-- =-=-=-=-=-=-= Call To Action =-=-=-=-=-=-= -->
<div class="parallex-small">
  <div class="container">
    <div class="row custom-padding-20">
      <div class="col-md-8 col-sm-8">
        <div class="parallex-text">
          <h4>Not sure which solution fits you business needs?</h4>
        </div>
        <!-- end subsection-text -->
      </div>
      <!-- end col-md-8 -->

      <div class="col-md-4 col-sm-4">
        <div class="parallex-button">
          <a href="{% url 'cargo_booking' %}" class="btn btn-lg btn-clean"
            >Book an order <i class="fa fa-angle-double-right"></i
          ></a>
        </div>
        <!-- end parallex-button -->
      </div>
      <!-- end col-md-4 -->
    </div>
    <!-- end row -->
  </div>
  <!-- end container -->
</div>
<!-- =-=-=-=-=-=-= Call To Action End =-=-=-=-=-=-= -->
{% endblock content %}
