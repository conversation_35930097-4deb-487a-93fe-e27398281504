from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from apps.exports.models import Item as ExportItem
from apps.imports.models import Item as ImportItem
from .models import ConsolidatedItem


@receiver(post_save, sender=ExportItem)
def sync_export_item_on_save(sender, instance, created, **kwargs):
    """
    Automatically sync export item to consolidated items when created or updated
    """
    try:
        ConsolidatedItem.sync_from_export_item(instance)
    except Exception as e:
        # Log the error but don't break the export item save
        print(f"Error syncing export item {instance.id}: {e}")


@receiver(post_save, sender=ImportItem)
def sync_import_item_on_save(sender, instance, created, **kwargs):
    """
    Automatically sync import item to consolidated items when created or updated
    """
    try:
        ConsolidatedItem.sync_from_import_item(instance)
    except Exception as e:
        # Log the error but don't break the import item save
        print(f"Error syncing import item {instance.id}: {e}")


@receiver(post_delete, sender=ExportItem)
def delete_consolidated_export_item(sender, instance, **kwargs):
    """
    Delete consolidated item when export item is deleted
    """
    try:
        ConsolidatedItem.objects.filter(
            export=instance.export,
            name=instance.name,
            item_type='export'
        ).delete()
    except Exception as e:
        print(f"Error deleting consolidated export item: {e}")


@receiver(post_delete, sender=ImportItem)
def delete_consolidated_import_item(sender, instance, **kwargs):
    """
    Delete consolidated item when import item is deleted
    """
    try:
        ConsolidatedItem.objects.filter(
            import_shipment=instance.imports,
            name=instance.name,
            item_type='import'
        ).delete()
    except Exception as e:
        print(f"Error deleting consolidated import item: {e}")