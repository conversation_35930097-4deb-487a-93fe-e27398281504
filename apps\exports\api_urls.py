from django.urls import path
from .api_views import (
    ExportCreateAPIView,
    ExportListAPIView,
    ExportDetailAPIView,
    ExportTrackingAPIView,
    ExportStatsAPIView
)

urlpatterns = [
    # Export management endpoints
    path('exports/', ExportCreateAPIView.as_view(), name='api_export_create'),
    path('exports/list/', ExportListAPIView.as_view(), name='api_export_list'),
    path('exports/<str:id>/', ExportDetailAPIView.as_view(), name='api_export_detail'),
    path('exports/stats/', ExportStatsAPIView.as_view(), name='api_export_stats'),
    
    # Public tracking endpoint
    path('tracking/export/<str:tracking_number>/', ExportTrackingAPIView.as_view(), name='api_export_tracking'),
]
