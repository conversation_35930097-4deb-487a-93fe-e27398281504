from django.core.management.base import BaseCommand
from apps.users.models import CustomUser


class Command(BaseCommand):

    help = "Auto creates admin users. Default password should be changed from the admin dashboard."

    def handle(self, *args, **options):
        if CustomUser.objects.filter(email="<EMAIL>").exists():
            self.stdout.write(self.style.WARNING(f"<EMAIL> already exists."))
            exit()
        admin_user = CustomUser.objects.create_superuser(
            first_name='mog',
            last_name="dynamics",
            email="<EMAIL>",
            password="password123"
        )
        admin_user.set_password("password123")
        admin_user.save()
        self.stdout.write(self.style.SUCCESS(f"Admin {admin_user.email} created"))