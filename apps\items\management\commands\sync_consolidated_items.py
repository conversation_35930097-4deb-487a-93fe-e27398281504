from django.core.management.base import BaseCommand
from apps.exports.models import Item as ExportItem
from apps.imports.models import Item as ImportItem
from apps.items.models import ConsolidatedItem


class Command(BaseCommand):
    help = 'Sync all existing export and import items to consolidated items'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear all existing consolidated items before syncing',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing consolidated items...')
            ConsolidatedItem.objects.all().delete()
            self.stdout.write(
                self.style.SUCCESS('Successfully cleared all consolidated items')
            )

        # Sync export items
        self.stdout.write('Syncing export items...')
        export_items = ExportItem.objects.select_related('export').all()
        export_count = 0
        
        for export_item in export_items:
            try:
                ConsolidatedItem.sync_from_export_item(export_item)
                export_count += 1
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'Error syncing export item {export_item.id}: {e}'
                    )
                )

        # Sync import items
        self.stdout.write('Syncing import items...')
        import_items = ImportItem.objects.select_related('imports').all()
        import_count = 0
        
        for import_item in import_items:
            try:
                ConsolidatedItem.sync_from_import_item(import_item)
                import_count += 1
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'Error syncing import item {import_item.id}: {e}'
                    )
                )

        total_consolidated = ConsolidatedItem.objects.count()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully synced {export_count} export items and '
                f'{import_count} import items. '
                f'Total consolidated items: {total_consolidated}'
            )
        )