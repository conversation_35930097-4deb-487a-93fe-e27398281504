import random


# Generate tracking number for export
def export_tracking_no():
    return f"EXP{str(random.randint(0000000000, 2147483647))}"


# Generate tracking number for import
def import_tracking_no():
    return f"IMP{str(random.randint(0000000000, 2147483647))}"


# Generate tracking number for import
def local_tracking_no():
    return f"LCL{str(random.randint(0000000000, 2147483647))}"


# To be used where needed
def random_numbers():
    return random.randint(0000000000, 2147483647)


# Generate reference number for export transaction
def generate_export_payment_reference_number():
    from apps.exports.models import Export
    reference = None
    while True:
        reference = str(random.randint(0000000000, 2147483647))
        if not Export.objects.values("payment_reference").filter(payment_reference=reference).exists():
            break
    return reference


# Generate reference number for import transaction
def generate_import_payment_reference_number():
    from apps.imports.models import Import
    reference = None
    while True:
        reference = str(random.randint(0000000000, 2147483647))
        if not Import.objects.values('payment_reference').filter(payment_reference=reference).exists():
            break
    return reference


# Generate reference number for logistics transaction
def generate_logistics_payment_reference_number():
    from apps.logistics.models import Logistic
    reference = None
    while True:
        reference = str(random.randint(0000000000, 2147483647))
        if not Logistic.objects.values("payment_reference").filter(payment_reference=reference).exists():
            break
    return reference


# Generates profile code for users on registration.
def generate_profile_code(fname, lname):
    code = str(fname[0]) + str(lname[0]) + "_MOG" + str(random.randint(0000000, 9999999))
    return code
