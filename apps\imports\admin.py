from django import forms
from django.contrib import admin
from .models import Import, Item
from phonenumber_field.widgets import PhoneNumberPrefixWidget
from core import tasks
from core.settings import base
from django.urls import reverse


# Automatically triggers the model object and sends mails to its associated user email.
def send_notification_email(modeladmin, request, queryset):
    for order in queryset:
        tracking_url = f'{request.get_host()}{reverse("track_import_shipping")}'
        custom_tracking_url = f'{request.get_host()}{reverse("import_search_result")}?tracking-no={order.tracking_no}'
        import_url = f'{request.get_host()}{order.get_absolute_url()}'
        email = order.customer.email
        subject = f'Import order {order.tracking_no} placed.'
        message = f"""
            Your order has been processed.Your tracking number is {order.tracking_no}.
            Visit {import_url} to view your order history and make outstanding payment.
            If you do not have an account with us yet, you can visit {tracking_url} to track your order
            with your tracking number or visit {custom_tracking_url} directly to track your order..
        """
        if order.email_sent == True:
            continue
        tasks.send_export_mail_task.delay(
            subject=subject,
            message=message,
            receiver_email=email,
            sender_email=base.DEFAULT_FROM_EMAIL,
        )
        order.email_sent = True
        order.save()
    message = f'Import notification email successfully sent to {email}'
    modeladmin.message_user(request, message)


# Changes model object's associated email_sent status to False.
def set_mailing_status_to_false(modeladmin, request, queryset):
    for order in queryset:
        order.email_sent = False
        order.save()
    message = 'status changed to {0}'.format(order.email_sent)
    modeladmin.message_user(request, message)


class ImportForm(forms.ModelForm):
    class Meta:
        widgets = {'phone_no': PhoneNumberPrefixWidget(initial="NGN")}


@admin.register(Item)
class AdminItem(admin.ModelAdmin):
    list_display = ('imports', 'name', 'quantity', 'price', 'total_price', 'weight')
    readonly_fields = ('id', 'total_price')
    search_fields = ('imports__tracking_no', 'name', 'imports', 'imports__customer__first_name',
                     'imports__customer__last_last_name')

    def customer_full_name(self, obj):
        return f"{obj.imports.customer.first_name} {obj.imports.customer.last_name}"

    # Enable sorting by full name
    customer_full_name.admin_order_field = 'export__customer__last_name'


@admin.register(Import)
class AdminImport(admin.ModelAdmin):
    form = ImportForm
    list_display = ('customer', 'sender', 'phone_no', 'display_weight', 'display_volumentary_weight',
                    'import_fee', 'payment_status', 'tracking_no', 'payment_reference', 'email_sent')
    search_fields = (
        "tracking_no",
        "customer__email",
        "payment_reference",
        "payment_status",
        "customer__first_name",
        "customer__last_name",
        "customer",
        "phone_no",
        "display_weight"
    )
    readonly_fields = ('tracking_no', 'payment_reference', 'display_weight', 'display_volumentary_weight')
    actions = [send_notification_email, set_mailing_status_to_false]

    def display_weight(self, obj):
        return obj.get_weight
    display_weight.short_description = "weight"

    def display_volumentary_weight(self, obj):
        return obj.get_volumentary_weight
    display_volumentary_weight.short_description = "volumentary weight"

    def customer_full_name(self, obj):
        return f"{obj.customer.first_name} {obj.customer.last_name}"

    # Enable sorting by full name
    customer_full_name.admin_order_field = 'customer__last_name'
