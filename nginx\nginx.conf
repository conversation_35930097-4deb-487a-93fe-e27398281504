worker_processes 8;
user root;
error_log  /var/log/nginx/error.log;
pid /var/run/nginx.pid;


events {
    worker_connections: 1024;
}


http {
    include       mime.types;

    upstream mogdynamics {
        server web:8000;
    }

    server {
        listen 80;

        server_name mogdynamics.com www.mogdynamics.com; # copy Your server name here

        location / {
            proxy_pass http://mogdynamics; # copy main hostname here
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-Proto https;
        }

        location /static/ { # copy your STATIC_URL here
            alias /mog_dynamics/staticfiles/; # copy your static file path here
        }
    }
}
