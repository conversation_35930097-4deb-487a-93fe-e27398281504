/********************************************
 * REVOLUTION 5.2.6 EXTENSION - PARALLAX
 * @version: 1.5 (23.06.2016)
 * @requires jquery.themepunch.revolution.js
 * <AUTHOR>
*********************************************/
!function(e){function r(e,r){e.lastscrolltop=r}var t=jQuery.fn.revolution,a=t.is_mobile();jQuery.extend(!0,t,{checkForParallax:function(e,r){function o(e){if("3D"==s.type||"3d"==s.type){e.find(".slotholder").wrapAll('<div class="dddwrapper" style="width:100%;height:100%;position:absolute;top:0px;left:0px;overflow:hidden"></div>'),e.find(".tp-parallax-wrap").wrapAll('<div class="dddwrapper-layer" style="width:100%;height:100%;position:absolute;top:0px;left:0px;z-index:5;overflow:'+s.ddd_layer_overflow+';"></div>'),e.find(".rs-parallaxlevel-tobggroup").closest(".tp-parallax-wrap").wrapAll('<div class="dddwrapper-layertobggroup" style="position:absolute;top:0px;left:0px;z-index:50;width:100%;height:100%"></div>');var t=e.find(".dddwrapper"),a=e.find(".dddwrapper-layer"),o=e.find(".dddwrapper-layertobggroup");o.appendTo(t),"carousel"==r.sliderType&&("on"==s.ddd_shadow&&t.addClass("dddwrappershadow"),punchgs.TweenLite.set(t,{borderRadius:r.carousel.border_radius})),punchgs.TweenLite.set(e,{overflow:"visible",transformStyle:"preserve-3d",perspective:1600}),punchgs.TweenLite.set(t,{force3D:"auto",transformOrigin:"50% 50%"}),punchgs.TweenLite.set(a,{force3D:"auto",transformOrigin:"50% 50%",zIndex:5}),punchgs.TweenLite.set(r.ul,{transformStyle:"preserve-3d",transformPerspective:1600})}}var s=r.parallax;if(!s.done){if(s.done=!0,a&&"on"==s.disable_onmobile)return!1;("3D"==s.type||"3d"==s.type)&&(punchgs.TweenLite.set(r.c,{overflow:s.ddd_overflow}),punchgs.TweenLite.set(r.ul,{overflow:s.ddd_overflow}),"carousel"!=r.sliderType&&"on"==s.ddd_shadow&&(r.c.prepend('<div class="dddwrappershadow"></div>'),punchgs.TweenLite.set(r.c.find(".dddwrappershadow"),{force3D:"auto",transformPerspective:1600,transformOrigin:"50% 50%",width:"100%",height:"100%",position:"absolute",top:0,left:0,zIndex:0}))),r.li.each(function(){o(jQuery(this))}),("3D"==s.type||"3d"==s.type&&r.c.find(".tp-static-layers").length>0)&&(punchgs.TweenLite.set(r.c.find(".tp-static-layers"),{top:0,left:0,width:"100%",height:"100%"}),o(r.c.find(".tp-static-layers"))),s.pcontainers=new Array,s.pcontainer_depths=new Array,s.bgcontainers=new Array,s.bgcontainer_depths=new Array,r.c.find(".tp-revslider-slidesli .slotholder, .tp-revslider-slidesli .rs-background-video-layer").each(function(){var e=jQuery(this),t=e.data("bgparallax")||r.parallax.bgparallax;t="on"==t?1:t,void 0!==t&&"off"!==t&&(s.bgcontainers.push(e),s.bgcontainer_depths.push(r.parallax.levels[parseInt(t,0)-1]/100))});for(var i=1;i<=s.levels.length;i++)r.c.find(".rs-parallaxlevel-"+i).each(function(){var e=jQuery(this),r=e.closest(".tp-parallax-wrap");r.data("parallaxlevel",s.levels[i-1]),r.addClass("tp-parallax-container"),s.pcontainers.push(r),s.pcontainer_depths.push(s.levels[i-1])});("mouse"==s.type||"scroll+mouse"==s.type||"mouse+scroll"==s.type||"3D"==s.type||"3d"==s.type)&&(e.mouseenter(function(r){var t=e.find(".active-revslide"),a=e.offset().top,o=e.offset().left,s=r.pageX-o,i=r.pageY-a;t.data("enterx",s),t.data("entery",i)}),e.on("mousemove.hoverdir, mouseleave.hoverdir, trigger3dpath",function(t,a){var o=a&&a.li?a.li:e.find(".active-revslide");if("enterpoint"==s.origo){var i=e.offset().top,l=e.offset().left;void 0==o.data("enterx")&&o.data("enterx",t.pageX-l),void 0==o.data("entery")&&o.data("entery",t.pageY-i);var n=o.data("enterx")||t.pageX-l,d=o.data("entery")||t.pageY-i,p=n-(t.pageX-l),c=d-(t.pageY-i),h=s.speed/1e3||.4}else var i=e.offset().top,l=e.offset().left,p=r.conw/2-(t.pageX-l),c=r.conh/2-(t.pageY-i),h=s.speed/1e3||3;"mouseleave"==t.type&&(p=s.ddd_lasth||0,c=s.ddd_lastv||0,h=1.5);for(var u=0;u<s.pcontainers.length;u++){var w=s.pcontainers[u],f=s.pcontainer_depths[u],v="3D"==s.type||"3d"==s.type?f/200:f/100,g=p*v,y=c*v;"scroll+mouse"==s.type||"mouse+scroll"==s.type?punchgs.TweenLite.to(w,h,{force3D:"auto",x:g,ease:punchgs.Power3.easeOut,overwrite:"all"}):punchgs.TweenLite.to(w,h,{force3D:"auto",x:g,y:y,ease:punchgs.Power3.easeOut,overwrite:"all"})}if("3D"==s.type||"3d"==s.type){var x=".tp-revslider-slidesli .dddwrapper, .dddwrappershadow, .tp-revslider-slidesli .dddwrapper-layer, .tp-static-layers .dddwrapper-layer";"carousel"===r.sliderType&&(x=".tp-revslider-slidesli .dddwrapper, .tp-revslider-slidesli .dddwrapper-layer, .tp-static-layers .dddwrapper-layer"),r.c.find(x).each(function(){var e=jQuery(this),a=s.levels[s.levels.length-1]/200,o=p*a,i=c*a,l=0==r.conw?0:Math.round(p/r.conw*a*100)||0,n=0==r.conh?0:Math.round(c/r.conh*a*100)||0,d=e.closest("li"),u=0,w=!1;e.hasClass("dddwrapper-layer")&&(u=s.ddd_z_correction||65,w=!0),e.hasClass("dddwrapper-layer")&&(o=0,i=0),d.hasClass("active-revslide")||"carousel"!=r.sliderType?"on"!=s.ddd_bgfreeze||w?punchgs.TweenLite.to(e,h,{rotationX:n,rotationY:-l,x:o,z:u,y:i,ease:punchgs.Power3.easeOut,overwrite:"all"}):punchgs.TweenLite.to(e,.5,{force3D:"auto",rotationY:0,rotationX:0,z:0,ease:punchgs.Power3.easeOut,overwrite:"all"}):punchgs.TweenLite.to(e,.5,{force3D:"auto",rotationY:0,z:0,x:0,y:0,rotationX:0,z:0,ease:punchgs.Power3.easeOut,overwrite:"all"}),"mouseleave"==t.type&&punchgs.TweenLite.to(jQuery(this),3.8,{z:0,ease:punchgs.Power3.easeOut})})}}),a&&(window.ondeviceorientation=function(t){var a=Math.round(t.beta||0)-70,o=Math.round(t.gamma||0),i=e.find(".active-revslide");if(jQuery(window).width()>jQuery(window).height()){var l=o;o=a,a=l}var n=e.width(),d=e.height(),p=360/n*o,c=180/d*a,h=s.speed/1e3||3,u=[];if(i.find(".tp-parallax-container").each(function(e){u.push(jQuery(this))}),e.find(".tp-static-layers .tp-parallax-container").each(function(){u.push(jQuery(this))}),jQuery.each(u,function(){var e=jQuery(this),r=parseInt(e.data("parallaxlevel"),0),t=r/100,a=p*t*2,o=c*t*4;punchgs.TweenLite.to(e,h,{force3D:"auto",x:a,y:o,ease:punchgs.Power3.easeOut,overwrite:"all"})}),"3D"==s.type||"3d"==s.type){var w=".tp-revslider-slidesli .dddwrapper, .dddwrappershadow, .tp-revslider-slidesli .dddwrapper-layer, .tp-static-layers .dddwrapper-layer";"carousel"===r.sliderType&&(w=".tp-revslider-slidesli .dddwrapper, .tp-revslider-slidesli .dddwrapper-layer, .tp-static-layers .dddwrapper-layer"),r.c.find(w).each(function(){var e=jQuery(this),a=s.levels[s.levels.length-1]/200;offsh=p*a,offsv=c*a*3,offrv=0==r.conw?0:Math.round(p/r.conw*a*500)||0,offrh=0==r.conh?0:Math.round(c/r.conh*a*700)||0,li=e.closest("li"),zz=0,itslayer=!1,e.hasClass("dddwrapper-layer")&&(zz=s.ddd_z_correction||65,itslayer=!0),e.hasClass("dddwrapper-layer")&&(offsh=0,offsv=0),li.hasClass("active-revslide")||"carousel"!=r.sliderType?"on"!=s.ddd_bgfreeze||itslayer?punchgs.TweenLite.to(e,h,{rotationX:offrh,rotationY:-offrv,x:offsh,z:zz,y:offsv,ease:punchgs.Power3.easeOut,overwrite:"all"}):punchgs.TweenLite.to(e,.5,{force3D:"auto",rotationY:0,rotationX:0,z:0,ease:punchgs.Power3.easeOut,overwrite:"all"}):punchgs.TweenLite.to(e,.5,{force3D:"auto",rotationY:0,z:0,x:0,y:0,rotationX:0,z:0,ease:punchgs.Power3.easeOut,overwrite:"all"}),"mouseleave"==t.type&&punchgs.TweenLite.to(jQuery(this),3.8,{z:0,ease:punchgs.Power3.easeOut})})}})),t.scrollTicker(r,e)}},scrollTicker:function(e,r){1!=e.scrollTicker&&(e.scrollTicker=!0,a?(punchgs.TweenLite.ticker.fps(150),punchgs.TweenLite.ticker.addEventListener("tick",function(){t.scrollHandling(e)},r,!1,1)):document.addEventListener("scroll",function(r){t.scrollHandling(e,!0)},{passive:!0})),t.scrollHandling(e,!0)},scrollHandling:function(e,o){if(e.lastwindowheight=e.lastwindowheight||window.innerHeight,e.lastscrolltop==window.scrollY&&!e.duringslidechange&&!o)return!1;punchgs.TweenLite.delayedCall(.2,r,[e,window.scrollY]);var s=e.c[0].getBoundingClientRect(),i=e.viewPort,l=e.parallax,n=s.top<0||s.height>e.lastwindowheight?s.top/s.height:s.bottom>e.lastwindowheight?(s.bottom-e.lastwindowheight)/s.height:0;if(e.scrollproc=n,t.callBackHandling&&t.callBackHandling(e,"parallax","start"),i.enable){var d=1-Math.abs(n);d=0>d?0:d,jQuery.isNumeric(i.visible_area)||-1!==i.visible_area.indexOf("%")&&(i.visible_area=parseInt(i.visible_area)/100),1-i.visible_area<=d?e.inviewport||(e.inviewport=!0,t.enterInViewPort(e)):e.inviewport&&(e.inviewport=!1,t.leaveViewPort(e))}if(a&&"on"==l.disable_onmobile)return!1;if("3d"!=l.type&&"3D"!=l.type){if(("scroll"==l.type||"scroll+mouse"==l.type||"mouse+scroll"==l.type)&&l.pcontainers)for(var p=0;p<l.pcontainers.length;p++)if(l.pcontainers[p].length>0){var c=l.pcontainers[p],h=l.pcontainer_depths[p]/100,u=Math.round(n*-(h*e.conh)*10)/10||0;c.data("parallaxoffset",u),punchgs.TweenLite.set(c,{overwrite:"auto",force3D:"auto",y:u})}if(l.bgcontainers)for(var p=0;p<l.bgcontainers.length;p++){var w=l.bgcontainers[p],f=l.bgcontainer_depths[p],u=n*-(f*e.conh)||0;punchgs.TweenLite.set(w,{position:"absolute",top:"0px",left:"0px",backfaceVisibility:"hidden",force3D:"true",y:u+"px"})}}t.callBackHandling&&t.callBackHandling(e,"parallax","end")}})}(jQuery);