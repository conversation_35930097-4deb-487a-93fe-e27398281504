{% extends "base.html" %}
{% load humanize %}

{% block title %}Items Dashboard - Statistics{% endblock %}

{% block extra_css %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 40px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        height: 100%;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-bottom: 20px;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 1.1rem;
    }
    
    .export-theme {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }
    
    .import-theme {
        background: linear-gradient(135deg, #007bff, #6610f2);
        color: white;
    }
    
    .total-theme {
        background: linear-gradient(135deg, #fd7e14, #e83e8c);
        color: white;
    }
    
    .status-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        margin-bottom: 20px;
    }
    
    .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .status-item:last-child {
        border-bottom: none;
    }
    
    .status-label {
        font-weight: 500;
        color: #495057;
    }
    
    .status-count {
        font-weight: bold;
        font-size: 1.2rem;
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        margin-bottom: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <h1 class="mb-3">
            <i class="fas fa-chart-line me-3"></i>
            Items Dashboard
        </h1>
        <p class="mb-0 opacity-75">Comprehensive overview of all consolidated export and import items</p>
    </div>

    <!-- Main Statistics -->
    <div class="row mb-4">
        <div class="col-lg-4 mb-4">
            <div class="stat-card total-theme">
                <div class="stat-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stat-number">{{ stats.total_items|default:0 }}</div>
                <div class="stat-label">Total Items</div>
                {% if stats.total_value %}
                <div class="mt-2">
                    <small>Total Value: ${{ stats.total_value|floatformat:2 }}</small>
                </div>
                {% endif %}
                {% if stats.total_weight %}
                <div>
                    <small>Total Weight: {{ stats.total_weight|floatformat:2 }} kg</small>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="stat-card export-theme">
                <div class="stat-icon">
                    <i class="fas fa-plane-departure"></i>
                </div>
                <div class="stat-number">{{ export_stats.count|default:0 }}</div>
                <div class="stat-label">Export Items</div>
                {% if export_stats.value %}
                <div class="mt-2">
                    <small>Value: ${{ export_stats.value|floatformat:2 }}</small>
                </div>
                {% endif %}
                {% if export_stats.weight %}
                <div>
                    <small>Weight: {{ export_stats.weight|floatformat:2 }} kg</small>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="stat-card import-theme">
                <div class="stat-icon">
                    <i class="fas fa-plane-arrival"></i>
                </div>
                <div class="stat-number">{{ import_stats.count|default:0 }}</div>
                <div class="stat-label">Import Items</div>
                {% if import_stats.value %}
                <div class="mt-2">
                    <small>Value: ${{ import_stats.value|floatformat:2 }}</small>
                </div>
                {% endif %}
                {% if import_stats.weight %}
                <div>
                    <small>Weight: {{ import_stats.weight|floatformat:2 }} kg</small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Shipping Status Breakdown -->
        <div class="col-lg-6 mb-4">
            <div class="status-card">
                <h4 class="mb-4">
                    <i class="fas fa-shipping-fast me-2 text-primary"></i>
                    Shipping Status Breakdown
                </h4>
                {% for status, count in status_breakdown.shipping.items %}
                <div class="status-item">
                    <span class="status-label">
                        <span class="badge bg-{% if status == 'Shipped' %}success{% elif status == 'Pending' %}warning text-dark{% else %}secondary{% endif %} me-2">
                            {{ status }}
                        </span>
                    </span>
                    <span class="status-count">{{ count }}</span>
                </div>
                {% empty %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-3"></i>
                    <p>No shipping data available</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Payment Status Breakdown -->
        <div class="col-lg-6 mb-4">
            <div class="status-card">
                <h4 class="mb-4">
                    <i class="fas fa-credit-card me-2 text-success"></i>
                    Payment Status Breakdown
                </h4>
                {% for status, count in status_breakdown.payment.items %}
                <div class="status-item">
                    <span class="status-label">
                        <span class="badge bg-{% if status == 'Paid' %}success{% else %}danger{% endif %} me-2">
                            {{ status }}
                        </span>
                    </span>
                    <span class="status-count">{{ count }}</span>
                </div>
                {% empty %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-3"></i>
                    <p>No payment data available</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="status-card">
                <h4 class="mb-4">
                    <i class="fas fa-bolt me-2 text-warning"></i>
                    Quick Actions
                </h4>
                <div class="d-flex flex-wrap gap-3">
                    <a href="{% url 'items:consolidated_items_list' %}" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i>
                        View All Items
                    </a>
                    <button class="btn btn-success" onclick="syncItems()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Sync Items
                    </button>
                    <a href="{% url 'items:consolidated_items_list' %}?shipping_status=Pending" class="btn btn-warning">
                        <i class="fas fa-clock me-2"></i>
                        View Pending Items
                    </a>
                    <a href="{% url 'items:consolidated_items_list' %}?payment_status=Unpaid" class="btn btn-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        View Unpaid Items
                    </a>
                    <a href="/admin/items/consolidateditem/" class="btn btn-info" target="_blank">
                        <i class="fas fa-cog me-2"></i>
                        Admin Panel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="status-card">
                <h4 class="mb-4">
                    <i class="fas fa-history me-2 text-info"></i>
                    System Information
                </h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item mb-3">
                            <strong>Last Updated:</strong>
                            <span class="text-muted">{{ "now"|date:"F d, Y g:i A" }}</span>
                        </div>
                        <div class="info-item mb-3">
                            <strong>Data Source:</strong>
                            <span class="text-muted">Export & Import Items</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item mb-3">
                            <strong>Auto-Sync:</strong>
                            <span class="text-muted">Manual sync required</span>
                        </div>
                        <div class="info-item mb-3">
                            <strong>Status:</strong>
                            <span class="badge bg-success">Active</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Sync items function
function syncItems() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Syncing...';
    btn.disabled = true;
    
    fetch('{% url "items:sync_items" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token }}',
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error syncing items: ' + error);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// Auto-refresh every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}