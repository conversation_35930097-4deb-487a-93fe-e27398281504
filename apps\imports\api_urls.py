from django.urls import path
from .api_views import (
    ImportCreateAPIView,
    ImportListAPIView,
    ImportDetailAPIView,
    ImportTrackingAPIView,
    ImportStatsAPIView
)

urlpatterns = [
    # Import management endpoints
    path('imports/', ImportCreateAPIView.as_view(), name='api_import_create'),
    path('imports/list/', ImportListAPIView.as_view(), name='api_import_list'),
    path('imports/<str:id>/', ImportDetailAPIView.as_view(), name='api_import_detail'),
    path('imports/stats/', ImportStatsAPIView.as_view(), name='api_import_stats'),
    
    # Public tracking endpoint
    path('tracking/import/<str:tracking_number>/', ImportTrackingAPIView.as_view(), name='api_import_tracking'),
]
