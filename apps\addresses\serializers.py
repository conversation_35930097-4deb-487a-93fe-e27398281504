from rest_framework import serializers
from .models import Address


class AddressSerializer(serializers.ModelSerializer):
    """Serializer for Address model"""
    phone_number_display = serializers.SerializerMethodField()
    short_address = serializers.SerializerMethodField()
    
    class Meta:
        model = Address
        fields = [
            'id', 'name', 'address', 'city', 'state', 'country',
            'phone_number', 'phone_number_display', 'short_address',
            'created_on', 'updated_on'
        ]
        read_only_fields = ['id', 'created_on', 'updated_on', 'phone_number_display', 'short_address']
    
    def get_phone_number_display(self, obj):
        """Get formatted phone number"""
        return str(obj.phone_number) if obj.phone_number else None
    
    def get_short_address(self, obj):
        """Get short address format"""
        return obj.get_short_address()


class AddressCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating Address"""
    
    class Meta:
        model = Address
        fields = [
            'name', 'address', 'city', 'state', 'country', 'phone_number'
        ]
    
    def validate(self, attrs):
        """Validate address data"""
        # Check for duplicate addresses for the same user
        user = self.context['request'].user
        name = attrs.get('name')
        address = attrs.get('address')
        
        if Address.objects.filter(
            user=user, 
            name__iexact=name,
            address__iexact=address
        ).exists():
            raise serializers.ValidationError(
                "You already have an address with this name and location."
            )
        
        return attrs
    
    def create(self, validated_data):
        """Create address for current user"""
        user = self.context['request'].user
        return Address.objects.create(user=user, **validated_data)


class AddressUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating Address"""
    
    class Meta:
        model = Address
        fields = [
            'name', 'address', 'city', 'state', 'country', 'phone_number'
        ]
    
    def validate(self, attrs):
        """Validate address update"""
        user = self.context['request'].user
        instance = self.instance
        name = attrs.get('name', instance.name)
        address = attrs.get('address', instance.address)
        
        # Check for duplicate addresses (excluding current instance)
        if Address.objects.filter(
            user=user,
            name__iexact=name,
            address__iexact=address
        ).exclude(id=instance.id).exists():
            raise serializers.ValidationError(
                "You already have an address with this name and location."
            )
        
        return attrs
