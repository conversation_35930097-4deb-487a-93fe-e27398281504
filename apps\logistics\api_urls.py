from django.urls import path
from .api_views import (
    LogisticCreateAPIView,
    LogisticOrdersListAPIView,
    LogisticDetailAPIView,
    DispatcherAssignmentsAPIView,
    LogisticStatusUpdateAPIView,
    LogisticTrackingAPIView
)

urlpatterns = [
    # Logistics management endpoints (Company users)
    path('logistics/', LogisticCreateAPIView.as_view(), name='api_logistic_create'),
    path('logistics/orders/', LogisticOrdersListAPIView.as_view(), name='api_logistic_orders_list'),
    path('logistics/<str:id>/', LogisticDetailAPIView.as_view(), name='api_logistic_detail'),
    
    # Dispatcher endpoints
    path('logistics/assignments/', DispatcherAssignmentsAPIView.as_view(), name='api_dispatcher_assignments'),
    path('logistics/<str:id>/status/', LogisticStatusUpdateAPIView.as_view(), name='api_logistic_status_update'),
    
    # Public tracking endpoint
    path('tracking/logistics/<str:tracking_number>/', LogisticTrackingAPIView.as_view(), name='api_logistic_tracking'),
]
