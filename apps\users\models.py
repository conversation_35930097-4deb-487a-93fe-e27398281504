from django.db import models
from django.dispatch import receiver
from apps.common import choices
from django.urls import reverse
from apps.common.models import TimeStampedModel
from apps.users.managers import CustomUserManager
from django.utils.translation import gettext_lazy as _
from apps.common.generator import generate_profile_code
from phonenumber_field.modelfields import PhoneN<PERSON>ber<PERSON>ield
from django.contrib.auth.models import PermissionsMixin, AbstractBaseUser
from django.db.models.signals import pre_delete
import cloudinary
from cloudinary.models import CloudinaryField


class CustomUser(TimeStampedModel, PermissionsMixin, AbstractBaseUser):
    username = None
    profile_code = models.Char<PERSON>ield(max_length=100, unique=True, blank=True, null=True)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    email = models.EmailField(unique=True, max_length=100)
    gender = models.CharField(default="male", choices=choices.gender_choices, max_length=10)
    phone_no = PhoneNumberField(blank=True, max_length=14)
    address = models.CharField(max_length=500)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    country = models.CharField(choices=choices.user_country, max_length=100)
    document_type = models.CharField(max_length=100, choices=choices.document_types)
    document = CloudinaryField(folder="static/media/images/user_documents/", use_filename=True, help_text=_(
        "upload a legal document that truely identifies you for verification."), blank=True, null=True)
    profile_picture = CloudinaryField(folder="static/media/images/profile_images/", use_filename=True,
                                      default="static/media/images/dummy_qr.5603f3d901b1", max_length=500, blank=True, null=True)
    user_type = models.CharField(max_length=50, choices=choices.user_type, default="regular")
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)

    objects = CustomUserManager()
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name']

    class Meta:
        verbose_name = "User"
        verbose_name_plural = "Users"

    def get_full_name(self):
        return f"{self.first_name.capitalize()} {self.last_name.capitalize()}"

    def get_user_address(self):
        return f"{self.address.capitalize()}, {self.city.capitalize()}, {self.state.capitalize()}, {self.country.capitalize()}."

    def __str__(self):
        return f"{self.get_full_name()} {self.profile_code}"

    def get_absolute_url(self):
        return reverse("user_detail", kwargs={"id": self.id})

    def get_business_name(self):
        from apps.company.models import Company
        company = Company.objects.filter(user_id=self.id).first()
        business_name = [word.capitalize() for word in company.business_name.split()]
        return ' '.join(business_name)

    def save(self, *args, **kwargs):
        self.profile_code = generate_profile_code(self.first_name.capitalize(), self.last_name.capitalize())
        return super().save(*args, **kwargs)


@receiver(pre_delete, sender=CustomUser)
def delete_model_object_and_associated_files(sender, instance, **kwargs):
    if instance.document:
        cloudinary.uploader.destroy(instance.document.public_id)
    elif instance.profile_picture:
        cloudinary.uploader.destroy(instance.profile_picture.public_id)
    else:
        pass
