{% extends 'dashboard.html' %}
{% load humanize %}
{% block title %} import details {% endblock title %}


{% block content %}
    
    <main role="main" class="container main bg-gold">
        {% if import %}
            <h3>View Import</h3>
            <p>
                <a class="link" href="{% url 'user_profile' %}">Home</a> | 
                <a class="link" href="{% url 'import_list' %}">Import</a></h1> |
                {{ import.tracking_no }}
            </p>
            <hr>

            <!--import basic info-->
            <div class="row mt-2 justify-content-center">
                <div class="col-12 col-sm-6 col-md-6 col-lg-6 cards mb-3">
                    <div class="card">
                        <h5 class="m-2 mt-3">{{ import.tracking_no }}</h5>
                        <hr>
                        <div class="row p-2 cards">
                            <div class="col-12 col-sm-4 col-md-4 col-lg-4 card">
                                <div class="sender-detail">
                                    <h5>Sender Details: </h5>
                                    <b>{{ import.sender.name|capfirst}}</b>
                                    <p>{{ import.sender.get_address}}</p>
                                </div>
                            </div>
                        
                            <div class="col-12 col-sm-4 col-md-4 col-lg-4 card">
                                <div class="receiver-detail">
                                    <h5>Customer: </h5>
                                    <b>{{ import.customer.get_full_name|capfirst }}</b>
                                    <p>{{ import.customer.get_user_address}}</p>
                                </div>
                            </div>

                            <div class="col-12 col-sm-4 col-md-4 col-lg-4 card">
                                <div class="receiver-detail">
                                    <h6>Destination Warehouse: </h6>
                                    <p><small>{{ import.destination.get_address }}</small></p>
                                </div>
                            </div>
                        </div>
                    
                        <div class="row">
                            <div class="col-12 col-sm-4 col-md-4 col-lg-4">
                                <div class="p-2 other-details">
                                    <h6>Origin Warehouse: </h6>
                                    <p><small>{{ import.sender.get_address }}</p>
                                </div>
                            </div>

                            <div class="col-12 col-sm-4 col-md-4 col-lg-4">
                                <div class="p-2 other-details">
                                    <h6>Weight: </h6>
                                    <p ><small>{{ import.get_weight}}</small></p>
                                </div>
                            </div>

                            <div class="col-12 col-sm-4 col-md-4 col-lg-4">
                                <div class="p-2 other-details">
                                    <h6>Volumentary Weight: </h6>
                                    <p><small>{{ import.get_volumentary_weight }}</small></p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 col-sm-4 col-md-4 col-lg-4">
                                <div class="p-2 other-details">
                                    <h6>No. of Deliveries: </h6>
                                    <p><small>{{ total_items }}</small></p>
                                </div>
                            </div>

                            <div class="col-12 col-sm-4 col-md-4 col-lg-4">
                                <div class="p-2 other-details">
                                    <h6>Date created: </h6>
                                    <p><small>{{ import.created_on }}</small></p>
                                </div>
                            </div>

                            <div class="col-12 col-sm-4 col-md-4 col-lg-4">
                                <div class="p-2 other-details">
                                    <h6>Last Updated: </h6>
                                    <p><small>{{ import.updated_on}}</small></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            
                <!--items list table-->
                <div class="col-12 col-sm-6 col-md-6 col-lg-6 cards mb-3">
                    <div class="card">
                        <div>
                            <h3 class="p-2 pt-2 pb-1">Delivery List</h3>
                            <hr>
                            <div class="table-responsive p-2 pt-2 pb-0" >
                                {% if items %}
                                    <table class="table table-sm table-striped table-dark rounded">
                                        <thead>
                                            <tr>
                                                <th scope="col">Name</th>
                                                <th scope="col">Quantity</th>
                                                <th scope="col">Price per item</th>
                                                <th scope="col">Weight</th>
                                                <th scope="col">Total Amount</th>
                                                {% comment %} <th scope="col"></th> {% endcomment %}
                                            </tr>
                                        </thead>
                                        <tbody class="table-group-divider">
                                            {% for item in items %}
                                                <tr scope="row">
                                                    <td>{{ item.name|capfirst }}</td>
                                                    <td>{{ item.quantity }}</td>
                                                    <td>{{ item.price }}</td>
                                                    <td>{{ item.weight }}</td>
                                                    <td>{{ item.total_price }}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                {% else %}
                                    <h1 class="text-center">You do not have any item available on this import.</h1>
                                {% endif %}
                            </div>
                        
                            <div class="p-2 pt-0 text-center">
                                <span class="step-links">
                                    {% if items.has_previous %}
                                        <a href="?page=1" class="btn btn-primary">first</a>
                                            <span class="text-primary">
                                                Page {{ items.number }} of {{ items.paginator.num_pages }}.
                                            </span>
                                        <a href="?page={{ items.previous_page_number }}" class="btn btn-success">previous</a>
                                    {% endif %}
                            
                                    {% if items.has_next %}
                                        <a href="?page={{ items.next_page_number }}" class="btn btn-primary">Next</a>
                                            <span class="text-primary">
                                                Page {{ items.number }} of {{ items.paginator.num_pages }}.
                                            </span>
                                        <a href="?page={{ items.paginator.num_pages }}" class="btn btn-success">Last</a>
                                    {% endif %}
                                </span>
                            </div>
                            {% if total_items %}
                                <h5 class="p-2 pt-1 pb-1">Total Items: {{ total_items }}</h5>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-sm-4 col-md-4 col-4 cards mb-3">
                    <div class="card text-center w-100 h-100">
                        <h4 class="mt-3">Shipping Status</h4>
                        {% if import.shipping_status %}
                            <hr>
                            {% if import.shipping_status == "delivered" %}
                                <td><span>{{ import.shipping_status|capfirst }}</span></td>
                            {% elif import.shipping_status == "pending" %}
                                <td><span>{{ import.shipping_status|capfirst }}</span></td>
                            {% elif import.shipping_status == "shipped" %}
                                <td><span>{{ import.shipping_status|capfirst }}</span></td>
                            {% elif import.shipping_status == "cancelled" %}
                                <td><span>{{ import.shipping_status|capfirst }}</span></td>
                            {% elif import.shipping_status == "in transit" %}
                                <td><span>{{ import.shipping_status }}</span></td>
                            {% else %}
                                <td><span>{{ import.shipping_status|capfirst }}</span></td>
                            {% endif %}
                        {% else %}
                            <p class="text-center">Shipping status not available.</p>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-12 col-sm-4 col-md-4 col-4 cards mb-3">
                    <div class="card text-center w-100 h-100">
                        <h4 class="mt-3">Attention!</h4>
                        <hr>
                        {% if import.attention %}
                            <div class="p-4">
                                <p class="text-danger"><b>{{ import.attention|capfirst }}</b></p>
                            </div>
                        {% else %}
                            <div class="p-4">
                                <p>You dont have any actions to take yet.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!--payment status column-->
                <div class="col-12 col-sm-4 col-md-4 col-4 cards mb-3">
                    <div class="card text-center p-2">
                        <h2>Payment Status</h2>
                        {% if import.payment_status == "unpaid" %}
                            <div class="w-100 text-danger">
                                <h1 class="">{{ import.payment_status|capfirst }}</h1>
                            </div>
                            <hr>
                            <p>Amount To Pay:</p>
                            <h3>NGN{{ import.import_fee|floatformat:"2g" }}</h3>
                            <div class="w-100">
                                <a class="btn btn-primary" href="{% url 'make_payment_for_import' %}">Pay with Paystack</a>
                            </div>
                        {% elif import.payment_status == "pending" %}
                            <div class="w-100 text-info">
                                <h1 class="">{{ import.payment_status|capfirst }}</h1>
                            </div>
                            <hr>
                            <p>Amount To be confirmed:</p>
                            <h3>NGN{{ import_fee|floatformat:"2g" }}</h3>
                            <div class="w-100">
                                <a class="btn btn-info disabled">Pending confirmation</a>
                            </div>
                        {% elif import.payment_status == "failed" %}
                            <div class="w-100 text-danger">
                                <h1 class="">{{ import.payment_status|capfirst }}</h1>
                            </div>
                            <hr>
                            <p>Amount To Pay:</p>
                            <h3>NGN{{ import.import_fee|floatformat:"2g" }}</h3>
                            <div class="w-100">
                                <a class="btn btn-primary" href="{% url 'make_payment_for_import' %}">Pay with Paystack</a>
                            </div>
                        {% else %}
                            <div class="w-100 text-success">
                                <h1 class="">{{ import.payment_status|capfirst }}</h1>
                            </div>
                            <hr>
                            <p>Amount Confirmed:</p>
                            <h3>NGN{{ import.import_fee|floatformat:"2g" }}</h3>
                            <div class="w-100">
                                <a class="btn btn-success disabled" href="">Payment Confirmed</a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% else %}
            <h1 class="text-center mt-5">Nothing to display.</h1>
        {% endif %}
    </main>
{% endblock content %}