{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Consolidated Shipments{% endblock %}

{% block content %}
<div class="shipment-list-container">
    <div class="header-section">
        <h1>Consolidated Import/Export Shipments</h1>
        <div class="header-actions">
            <a href="{% url 'consolidated_dashboard' %}" class="btn btn-secondary">Dashboard</a>
            <a href="{% url 'sync_data' %}" class="btn btn-warning">Sync Data</a>
            <a href="/admin/consolidated_admin/consolidatedshipment/add/" class="btn btn-success">Add Shipment</a>
        </div>
    </div>
    
    <!-- Filters and Search -->
    <div class="filters-section">
        <form method="get" class="filter-form">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="type">Type:</label>
                    <select name="type" id="type">
                        <option value="">All Types</option>
                        {% for value, label in shipment_types %}
                            <option value="{{ value }}" {% if current_type == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="status">Status:</label>
                    <select name="status" id="status">
                        <option value="">All Statuses</option>
                        <option value="Pending" {% if current_status == 'Pending' %}selected{% endif %}>Pending</option>
                        <option value="Processing" {% if current_status == 'Processing' %}selected{% endif %}>Processing</option>
                        <option value="Shipped" {% if current_status == 'Shipped' %}selected{% endif %}>Shipped</option>
                        <option value="Delivered" {% if current_status == 'Delivered' %}selected{% endif %}>Delivered</option>
                        <option value="Cancelled" {% if current_status == 'Cancelled' %}selected{% endif %}>Cancelled</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="payment">Payment:</label>
                    <select name="payment" id="payment">
                        <option value="">All Payments</option>
                        <option value="Paid" {% if current_payment == 'Paid' %}selected{% endif %}>Paid</option>
                        <option value="Unpaid" {% if current_payment == 'Unpaid' %}selected{% endif %}>Unpaid</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="search">Search:</label>
                    <input type="text" name="search" id="search" value="{{ search_query }}" 
                           placeholder="Tracking no, customer name, email...">
                </div>
                
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="{% url 'consolidated_shipment_list' %}" class="btn btn-secondary">Clear</a>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Results -->
    <div class="results-section">
        <div class="results-info">
            <p>Showing {{ shipments|length }} of {{ paginator.count|default:shipments|length }} shipments</p>
        </div>
        
        <div class="table-container">
            <table class="shipments-table">
                <thead>
                    <tr>
                        <th>Tracking No</th>
                        <th>Type</th>
                        <th>Customer</th>
                        <th>From → To</th>
                        <th>Weight</th>
                        <th>Fee</th>
                        <th>Payment</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for shipment in shipments %}
                    <tr>
                        <td>
                            <a href="{% url 'consolidated_shipment_detail' shipment.pk %}" class="tracking-link">
                                {{ shipment.tracking_no }}
                            </a>
                        </td>
                        <td>
                            <span class="badge badge-{{ shipment.shipment_type }}">
                                {{ shipment.get_shipment_type_display }}
                            </span>
                        </td>
                        <td>
                            <div class="customer-info">
                                <strong>{{ shipment.customer.first_name }} {{ shipment.customer.last_name }}</strong>
                                <br><small>{{ shipment.customer.email }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="route-info">
                                {% if shipment.sender %}
                                    {{ shipment.sender.city }}, {{ shipment.sender.country }}
                                {% else %}
                                    N/A
                                {% endif %}
                                →
                                {% if shipment.destination %}
                                    {{ shipment.destination.city }}, {{ shipment.destination.country }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ shipment.get_weight }}</td>
                        <td>${{ shipment.shipping_fee|floatformat:2|default:"0.00" }}</td>
                        <td>
                            <span class="badge badge-payment-{{ shipment.payment_status|lower }}">
                                {{ shipment.payment_status }}
                            </span>
                        </td>
                        <td>
                            <span class="badge badge-status-{{ shipment.shipping_status|lower }}">
                                {{ shipment.shipping_status }}
                            </span>
                        </td>
                        <td>{{ shipment.created_on|date:"M d, Y" }}</td>
                        <td>
                            <div class="action-buttons">
                                <a href="{% url 'consolidated_shipment_detail' shipment.pk %}" 
                                   class="btn btn-sm btn-info">View</a>
                                <a href="/admin/consolidated_admin/consolidatedshipment/{{ shipment.pk }}/change/" 
                                   class="btn btn-sm btn-primary">Edit</a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="10" class="no-results">
                            No shipments found. 
                            {% if search_query or current_type or current_status or current_payment %}
                                <a href="{% url 'consolidated_shipment_list' %}">Clear filters</a> to see all shipments.
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="pagination-container">
            <div class="pagination">
                {% if page_obj.has_previous %}
                    <a href="?page=1{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.payment %}&payment={{ request.GET.payment }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" class="page-link">First</a>
                    <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.payment %}&payment={{ request.GET.payment }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" class="page-link">Previous</a>
                {% endif %}
                
                <span class="page-info">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.payment %}&payment={{ request.GET.payment }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" class="page-link">Next</a>
                    <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.type %}&type={{ request.GET.type }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.payment %}&payment={{ request.GET.payment }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" class="page-link">Last</a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<style>
.shipment-list-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #dee2e6;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.filters-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #495057;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.results-section {
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.results-info {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

.table-container {
    overflow-x: auto;
}

.shipments-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.shipments-table th,
.shipments-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
    vertical-align: top;
}

.shipments-table th {
    background-color: #e9ecef;
    font-weight: bold;
    position: sticky;
    top: 0;
}

.tracking-link {
    font-weight: bold;
    color: #007bff;
    text-decoration: none;
}

.tracking-link:hover {
    text-decoration: underline;
}

.customer-info strong {
    color: #495057;
}

.customer-info small {
    color: #6c757d;
}

.route-info {
    font-size: 13px;
    color: #495057;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75em;
    font-weight: bold;
    color: white;
    white-space: nowrap;
}

.badge-import { background-color: #007bff; }
.badge-export { background-color: #28a745; }
.badge-status-pending { background-color: #ffc107; color: #212529; }
.badge-status-processing { background-color: #17a2b8; }
.badge-status-shipped { background-color: #28a745; }
.badge-status-delivered { background-color: #155724; }
.badge-status-cancelled { background-color: #dc3545; }
.badge-payment-paid { background-color: #28a745; }
.badge-payment-unpaid { background-color: #dc3545; }

.action-buttons {
    display: flex;
    gap: 5px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-weight: bold;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    font-size: 14px;
    transition: opacity 0.2s;
}

.btn:hover {
    opacity: 0.8;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-primary { background-color: #007bff; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }
.btn-success { background-color: #28a745; color: white; }
.btn-warning { background-color: #ffc107; color: #212529; }
.btn-info { background-color: #17a2b8; color: white; }

.no-results {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-style: italic;
}

.pagination-container {
    padding: 20px;
    text-align: center;
    border-top: 1px solid #dee2e6;
}

.pagination {
    display: inline-flex;
    gap: 10px;
    align-items: center;
}

.page-link {
    padding: 8px 12px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
}

.page-link:hover {
    background-color: #0056b3;
}

.page-info {
    padding: 8px 12px;
    color: #495057;
    font-weight: bold;
}

@media (max-width: 768px) {
    .header-section {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .filter-row {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>
{% endblock %}