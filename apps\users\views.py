from apps.common.custom_validators import convert_to_megabyte
from apps.company.models import Company
from core import tasks
from .models import CustomUser
from django.views import generic
from .forms import UserUpdateForm
from django.contrib import messages
from django.urls import reverse_lazy
from apps.exports.models import Export
from apps.imports.models import Import
from apps.addresses.models import Address
from apps.logistics.models import Logistic
from apps.common.decorators import is_authenticated, is_disptacher
from django.contrib.auth.decorators import login_required
from django.contrib.auth import login, logout, authenticate
from django.shortcuts import render, redirect, get_object_or_404
from django.utils.encoding import force_bytes
from django.contrib.auth.tokens import default_token_generator
from .forms import CustomPasswordResetForm, CustomPasswordConfirmForm
from django.contrib.auth.views import PasswordResetConfirmView, PasswordResetView
from django.db.models import Count, Q
from django.db import transaction
from core.settings import base
from django.core.files.storage import FileSystemStorage
import mimetypes
import cloudinary
from django.contrib.auth.models import Permission

from .token import account_activation_token
from django.utils.html import strip_tags
from django.template.loader import render_to_string
from django.contrib.sites.shortcuts import get_current_site
from django.utils.encoding import force_bytes, force_str
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode


# User registration.
@is_authenticated
@transaction.atomic
def create_user(request):

    template_name = "users/register.html"
    file_types = ["image/png", "image/jpeg", "image/jpg", "application/pdf"]

    if request.method == 'POST' and request.FILES:
        first_name = request.POST.get("first-name", None)
        last_name = request.POST.get("last-name")
        gender = request.POST.get("gender", None)
        phone_no = request.POST.get("phone-no", None)
        address = request.POST.get("address", None)
        city = request.POST.get("city", None)
        state = request.POST.get("state", None)
        country = request.POST.get("country", None)
        document_type = request.POST.get("document-type", None)
        document = request.FILES.get("document", None)
        email = request.POST.get("email", None)
        password = request.POST.get("password", None)
        confirm_password = request.POST.get("confirm-password", None)

        if CustomUser.objects.filter(email=email).exists():
            messages.info(request, f"Email already exist.")
            return redirect("create_account")

        if password != confirm_password:
            messages.error(request, f"Passwords do not match!")
            return redirect("create_account")

        if not document:
            messages.error(request, f"Please upload your identification document.")
            return redirect("create_account")

        if document.size > base.FILE_UPLOAD_MAX_MEMORY_SIZE:
            messages.warning(
                request, f"Document cannot be larger than {convert_to_megabyte(base.FILE_UPLOAD_MAX_MEMORY_SIZE)}MB.")
            return redirect("create_account")

        fs = FileSystemStorage()
        filename = fs.save(document.name, document)
        file_type = mimetypes.guess_type(filename)[0]

        if file_type not in file_types:
            messages.info(request, "Invalid file uploaded, please upload an image (jpeg, png, jpg only).")
            return redirect('create_account')

        user = CustomUser.objects.create_user(
            first_name=first_name,
            last_name=last_name,
            email=email,
            gender=gender,
            phone_no=phone_no,
            address=address,
            city=city,
            state=state,
            country=country,
            document_type=document_type,
            document=document,
            password=password
        )
        user.set_password(password)
        user.user_type = "regular"
        user.is_active = False
        user.save()

        context = {
            'user': user,
            'domain': get_current_site(request).domain,
            'uid': urlsafe_base64_encode(force_bytes(user.id)),
            'token': account_activation_token.make_token(user),
        }
        html_message = render_to_string('users/activate_account.html', context)
        message = strip_tags(html_message)
        tasks.send_account_activation_mail_task.delay(
            subject="Activate your Account.",
            message=message,
            receiver_email=email,
            sender_email=base.DEFAULT_FROM_EMAIL
        )
        messages.success(
            request, f'account with {user.email} has been created, kindly check your inbox or spam for activation link.')
        return redirect('user_profile')
    return render(request, template_name)


# activate account
def account_activation(request, uidb64, token):
    subject = "Account Activation Status"
    message = "Your account has been successfully activate, you can proceed to login if you are already not logged in."
    uid = None
    user = None
    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = get_object_or_404(CustomUser, id=uid)
    except:
        pass

    if user is not None and account_activation_token.check_token(user, token):
        user.is_active = True
        user.save()
        tasks.send_account_activation_status_mail_task.delay(
            subject=subject,
            message=message,
            receiver_email=user.email,
            sender_email=base.DEFAULT_FROM_EMAIL
        )
        messages.success(request, "Account activation successful, you can proceed to login.")
        return redirect("user_login")
    messages.error(request, "Account activation failed, kindly check your inbox or spam for valid activation link.")
    return redirect("user_login")


# User details.
@login_required(login_url="user_login")
@is_disptacher
def user_profile(request):
    template_name = "users/profile.html"
    query = request.GET.get("address", "")

    company_addresses = Address.objects.all()
    selected_address = Address.objects.filter(address__icontains=query).first()

    imports = Import.objects.select_related("customer").filter(customer=request.user).aggregate(
        total_orders=Count('id'),
        paid_orders=Count('id', filter=Q(payment_status='paid')),
        unpaid_orders=Count('id', filter=Q(payment_status='unpaid')))

    exports = Export.objects.select_related("customer").filter(customer=request.user).aggregate(
        total_orders=Count('id'),
        paid_orders=Count('id', filter=Q(payment_status='paid')),
        unpaid_orders=Count('id', filter=Q(payment_status='unpaid')))

    logistics = Logistic.objects.select_related("customer").filter(company__user=request.user).aggregate(
        total_orders=Count('id'),
        paid_orders=Count('id', filter=Q(payment_status='paid')),
        unpaid_orders=Count('id', filter=Q(payment_status='unpaid')))

    context = {
        "user": request.user,
        "total_imports": imports["total_orders"],
        "paid_imports": imports["paid_orders"],
        "unpaid_imports": imports["unpaid_orders"],

        "total_exports": exports["total_orders"],
        "paid_exports": exports["paid_orders"],
        "unpaid_exports": exports["unpaid_orders"],

        "total_orders": logistics["total_orders"],
        "paid_orders": logistics["paid_orders"],
        "unpaid_orders": logistics["unpaid_orders"],

        "address_list": company_addresses,
        "selected_address": selected_address,
    }
    return render(request, template_name, context)


# filters company's address on user_profile page without page reload togetehr with htmx
@login_required(login_url="user_login")
def company_address(request):
    query = request.GET.get("address", "")
    template_name = "partial/company_address.html"
    selected_address = Address.objects.get(address=query)
    context = {"user": request.user, "selected_address": selected_address}
    return render(request, template_name, context)


# User profile update.
@login_required(login_url="user_login")
@is_disptacher
def update_profile(request, id):
    template_name = "users/update.html"
    user_obj = get_object_or_404(CustomUser, id=id)
    business_details = Company.objects.select_related('user').filter(user=request.user).first()
    old_image = user_obj.profile_picture.public_id

    if request.method == 'POST' and request.FILES:

        form = UserUpdateForm(request.POST, request.FILES, instance=user_obj)

        if form.is_valid():

            update_form = form.save(commit=False)
            profile_picture = form.cleaned_data.get("profile_picture", None)

            if profile_picture:

                if profile_picture.size > base.FILE_UPLOAD_MAX_MEMORY_SIZE:
                    messages.warning(
                        request, f"profile picture cannot be larger than {convert_to_megabyte(base.FILE_UPLOAD_MAX_MEMORY_SIZE)}MB.")
                    return redirect("update_profile", id=id)

                if old_image is not None:
                    cloudinary.uploader.destroy(old_image)

            update_form.save()
            messages.success(request, f"Profile updated successfully.")
            return redirect("update_profile", id=id)

        messages.error(request, "Unable to update profile")
        return redirect("update_profile", id=id)
    else:
        form = UserUpdateForm(instance=user_obj)
    context = {"form": form, "user": user_obj, "business_details": business_details}
    return render(request, template_name, context)


# User login
@is_authenticated
def user_login(request):
    template_name = 'users/login.html'
    if request.method == 'POST':
        email = request.POST.get("email")
        password = request.POST.get("password")

        if not CustomUser.objects.filter(email=email).exists():
            messages.warning(request, f"This email doesn't exist on our record.")
            return redirect("user_login")

        user_obj = CustomUser.objects.filter(email=email).first()
        if user_obj is not None and user_obj.is_active == False:
            context = {
                'user': user_obj,
                'domain': get_current_site(request).domain,
                'uid': urlsafe_base64_encode(force_bytes(user_obj.id)),
                'token': account_activation_token.make_token(user_obj),
            }
            html_message = render_to_string('users/activate_account.html', context)
            message = strip_tags(html_message)
            tasks.send_account_activation_mail_task.delay(
                subject="Activate your Account.",
                message=message,
                receiver_email=email,
                sender_email=base.DEFAULT_FROM_EMAIL
            )
            messages.warning(
                request, f"Your email has not been activated, check your email for link to activate your account then return to login.")

        user = authenticate(username=email, password=password)

        if user is not None:
            login(request, user)
            if "next" in request.POST:
                messages.success(request, f"Login successful.")
                return redirect(request.POST.get("next"))

            if user.user_type == "dispatcher":
                messages.success(request, f"Login successful.")
                return redirect("local_order_delivery_list")

            messages.success(request, f"Login successful.")
            return redirect("user_profile")

        messages.error(request, f"Invalid email and/or password combination, please try again.")
        return redirect("user_login")
    return render(request, template_name)


# User logout.
def user_logout(request):
    logout(request)
    messages.success(request, f"Logout successful.")
    return redirect("user_login")


# Password Reset. Supply email.
class CustomPasswordResetView(PasswordResetView):
    form_class = CustomPasswordResetForm
    template_name = 'users/password_reset_form.html'
    success_url = reverse_lazy('custom_password_reset_sent')

    def form_valid(self, form):
        email = form.cleaned_data.get("email")
        user = get_object_or_404(CustomUser, email=email)
        token = default_token_generator.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.id))
        reset_url = reverse_lazy('password_reset_confirm', kwargs={'uidb64': uid, 'token': token})
        valid_reset_url = self.request.build_absolute_uri(reset_url)
        context = {"valid_reset_link": valid_reset_url, "user": user, "domain": self.request.get_host()}
        message = render_to_string("users/password_reset_email_link.html", context)
        tasks.send_password_reset_task.delay(
            subject="Password reset",
            message=message,
            receiver_email=email,
            sender_email=base.DEFAULT_FROM_EMAIL
        )
        messages.success(
            self.request, f'Password reset request sent succesfully. You should receive your reset link in less than 3 minutes.')
        return super().form_valid(form)


class CustomPasswordResetSentView(generic.TemplateView):
    template_name = 'users/password_reset_sent.html'


class CustomPasswordResetConfirmView(PasswordResetConfirmView):
    template_name = 'users/password_reset_confirm.html'
    success_url = reverse_lazy('custom_password_reset_complete')
    form_class = CustomPasswordConfirmForm

    def form_valid(self, form):
        messages.success(self.request, f"Password reset successful.")
        return super().form_valid(form)


class CustomPasswordResetCompleteView(generic.TemplateView):
    template_name = 'users/password_reset_complete.html'
