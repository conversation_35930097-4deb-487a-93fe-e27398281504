name: Deploy to DigitalOcean

on:
  push:
    branches:
      - main  # Change this to the branch you want to trigger the deployment

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.5.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

    - name: Install dependencies and build app
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USERNAME }}@${{ secrets.SERVER_HOST }} 'cd ./mog_dynamics && git pull && pip install -r requirements.txt && python manage.py collectstatic --noinput'

    - name: Restart Gunicorn or uWSGI service
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USERNAME }}@${{ secrets.SERVER_HOST }} 'sudo systemctl daemon-reload'
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USERNAME }}@${{ secrets.SERVER_HOST }} 'sudo systemctl restart gunicorn'

    - name: Restart Nginx or other web server
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USERNAME }}@${{ secrets.SERVER_HOST }} 'sudo systemctl restart nginx'
      