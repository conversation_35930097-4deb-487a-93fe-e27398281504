﻿{% extends 'dashboard.html' %}


{% block content %}
    <!-- =-=-=-=-=-=-= Tracking History =-=-=-=-=-=-= -->
    <section id="order-tracking" class="main container section-padding-80">
        <h3 class="color-black">My Imports</h3>
        <p>
            <a class="link" href="{% url 'user_profile' %}">Home</a> | 
            <a class="link" href="{% url 'import_list' %}">Imports</a></h1> |
            view
        </p>
        <hr>
        {% if imports %}
            <div class="">
                <!-- Row -->
                <div class="row">
                    <div class="col-sm-12 col-xs-12 col-md-12 col-lg-12 p-2">
                        <!-- Search Filter -->
                        <div class="tracking-search">
                        <div class="input-group" id="adv-search">
                            <input type="text" class="form-control" placeholder="Track Your Shipment" />
                            <div class="input-group-btn">
                                <div class="btn-group" role="group">
                                    <div class="dropdown dropdown-lg">
                                        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-expanded="false"><span class="caret"></span></button>
                                        <div class="dropdown-menu dropdown-menu-right" role="menu">
                                            <form class="form-horizontal" role="form">
                                            <div class="form-group">
                                                <label for="filter">Filter by</label>
                                                <select class="form-control">
                                                    <option value="0" selected>Order History</option>
                                                    <option value="delivered">Delivered</option>
                                                    <option value="shipped">Shipped</option>
                                                    <option value="pending">Pending</option>
                                                    <option value="cancelled">Canceled</option>
                                                </select>
                                            </div>
                                            </form>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Search Filter End -->

            <!-- Tracking History -->
            <div class="block-content">
                <div class="table-responsive">
                    <table class="table table-clean-paddings margin-bottom-0">
                        <thead>
                            <tr>
                                <th>Sender</th>
                                <th>Shipped From</th>
                                <th>Shipping Id</th>
                                <th>Destination</th>
                                <th>Payment Status</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for import in imports %}
                                <tr>
                                    <td>
                                        <div class="contact-container">{{ import.sender.name }}<span>on {{ import.created_on|date }}</span></div>
                                    </td>
                                    <td>{{ import.sender.country }}</td>
                                    <td>{{ import.tracking_no }}</td>
                                    <td>{{ import.receiver_country }}</td>
                                    <td>
                                        {% if import.payment_status == "paid" %}
                                            <span class="label label-success">{{ import.payment_status|capfirst }}</span>
                                        {% else %}
                                            <span class="label label-danger">{{ import.payment_status|capfirst }}</span>
                                        {% endif %}
                                    </td>
                                    {% if import.shipping_status == "delivered" %}
                                        <td><span class="label label-success label-transparent">{{ import.shipping_status|capfirst }}</span></td>
                                    {% elif import.shipping_status == "pending" %}
                                        <td><span class="label label-black label-transparent">{{ import.shipping_status|capfirst }}</span></td>
                                    {% elif import.shipping_status == "shipped" %}
                                        <td><span class="label label-warning label-transparent">{{ import.shipping_status|capfirst }}</span></td>
                                    {% elif import.shipping_status == "cancelled" %}
                                        <td><span class="label label-danger label-transparent">{{ import.shipping_status|capfirst }}</span></td>
                                    {% elif import.shipping_status == "in transit" %}
                                        <td><span class="label label-success label-transparent">{{ import.shipping_status }}</span></td>
                                    {% else %}
                                        <td><span class="label label-success label-transparent">{{ import.shipping_status|capfirst|slice:20 }}...</span></td>
                                    {% endif %}
                                    <td><small><a href="{{ import.get_absolute_url }}">View Record</a></small></td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        {% else %}
            <h1 class="text-center">You have no import records.</h1>
        {% endif %}
    </section>
    <!-- =-=-=-=-=-=-= Tracking History End =-=-=-=-=-=-= -->
{% endblock content %}