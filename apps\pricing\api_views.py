from rest_framework import generics, permissions, status
from rest_framework.response import Response
from .models import Pricing
from .serializers import PricingSerializer


class PricingListAPIView(generics.ListAPIView):
    """API View to list all pricing information"""
    queryset = Pricing.objects.all()
    serializer_class = PricingSerializer
    permission_classes = [permissions.AllowAny]  # Public endpoint
    
    def list(self, request, *args, **kwargs):
        """List all pricing information"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class PricingDetailAPIView(generics.RetrieveAPIView):
    """API View to get specific pricing information"""
    queryset = Pricing.objects.all()
    serializer_class = PricingSerializer
    permission_classes = [permissions.AllowAny]  # Public endpoint
    lookup_field = 'id'
    
    def retrieve(self, request, *args, **kwargs):
        """Get specific pricing information"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)
