from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import login
from django.contrib.sites.shortcuts import get_current_site
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.utils.encoding import force_bytes, force_str
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.db import transaction
from core import tasks
from core.settings import base
from .models import CustomUser
from .serializers import (
    UserRegistrationSerializer, 
    UserLoginSerializer, 
    UserProfileSerializer,
    UserUpdateSerializer,
    AccountActivationSerializer
)
from .token import account_activation_token
from apps.exports.models import Export
from apps.imports.models import Import
from apps.logistics.models import Logistic


class UserRegistrationAPIView(APIView):
    """API View for user registration"""
    permission_classes = [permissions.AllowAny]
    
    @transaction.atomic
    def post(self, request):
        """Register a new user"""
        serializer = UserRegistrationSerializer(data=request.data)
        
        if serializer.is_valid():
            user = serializer.save()
            
            # Send activation email
            context = {
                'user': user,
                'domain': get_current_site(request).domain,
                'uid': urlsafe_base64_encode(force_bytes(user.id)),
                'token': account_activation_token.make_token(user),
            }
            html_message = render_to_string('users/activate_account.html', context)
            message = strip_tags(html_message)
            
            # Send email asynchronously
            tasks.send_account_activation_mail_task.delay(
                subject="Activate your Account.",
                message=message,
                receiver_email=user.email,
                sender_email=base.DEFAULT_FROM_EMAIL
            )
            
            return Response({
                'success': True,
                'message': 'Account created successfully. Check email for activation link.',
                'data': {
                    'user_id': str(user.id),
                    'email': user.email,
                    'profile_code': user.profile_code
                }
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'message': 'Registration failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UserLoginAPIView(APIView):
    """API View for user login"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        """Login user and return JWT tokens"""
        serializer = UserLoginSerializer(data=request.data)
        
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token
            
            # Login user (for session-based tracking if needed)
            login(request, user)
            
            return Response({
                'success': True,
                'message': 'Login successful',
                'data': {
                    'access_token': str(access_token),
                    'refresh_token': str(refresh),
                    'user': {
                        'id': str(user.id),
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'user_type': user.user_type,
                        'profile_picture': user.profile_picture.url if user.profile_picture else None,
                        'is_active': user.is_active
                    }
                }
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': 'Login failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UserProfileAPIView(generics.RetrieveUpdateAPIView):
    """API View for user profile management"""
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        """Return the current user"""
        return self.request.user
    
    def get_serializer_class(self):
        """Return appropriate serializer based on request method"""
        if self.request.method == 'PUT' or self.request.method == 'PATCH':
            return UserUpdateSerializer
        return UserProfileSerializer
    
    def retrieve(self, request, *args, **kwargs):
        """Get user profile"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)
    
    def update(self, request, *args, **kwargs):
        """Update user profile"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        
        if serializer.is_valid():
            serializer.save()
            
            # Return updated profile data
            profile_serializer = UserProfileSerializer(instance)
            
            return Response({
                'success': True,
                'message': 'Profile updated successfully',
                'data': profile_serializer.data
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': 'Profile update failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UserDashboardAPIView(APIView):
    """API View for user dashboard statistics"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get user dashboard statistics"""
        user = request.user
        
        # Get user statistics
        total_exports = Export.objects.filter(customer=user).count()
        total_imports = Import.objects.filter(customer=user).count()
        total_logistics = 0
        
        # For company users, get logistics count
        if hasattr(user, 'company') and user.company.exists():
            company = user.company.first()
            total_logistics = Logistic.objects.filter(company=company).count()
        
        # Get pending payments count
        pending_payments = (
            Export.objects.filter(customer=user, payment_status='Unpaid').count() +
            Import.objects.filter(customer=user, payment_status='Unpaid').count()
        )
        
        # Get recent shipments (last 5)
        recent_exports = Export.objects.filter(customer=user).order_by('-created_on')[:3]
        recent_imports = Import.objects.filter(customer=user).order_by('-created_on')[:3]
        
        recent_shipments = []
        
        # Add exports
        for export in recent_exports:
            recent_shipments.append({
                'id': str(export.id),
                'type': 'export',
                'tracking_no': export.tracking_no,
                'status': export.shipping_status,
                'created_on': export.created_on
            })
        
        # Add imports
        for import_item in recent_imports:
            recent_shipments.append({
                'id': str(import_item.id),
                'type': 'import',
                'tracking_no': import_item.tracking_no,
                'status': import_item.shipping_status,
                'created_on': import_item.created_on
            })
        
        # Sort by creation date
        recent_shipments.sort(key=lambda x: x['created_on'], reverse=True)
        recent_shipments = recent_shipments[:5]  # Limit to 5 most recent
        
        return Response({
            'success': True,
            'data': {
                'total_exports': total_exports,
                'total_imports': total_imports,
                'total_logistics': total_logistics,
                'pending_payments': pending_payments,
                'recent_shipments': recent_shipments
            }
        }, status=status.HTTP_200_OK)


class AccountActivationAPIView(APIView):
    """API View for account activation"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        """Activate user account"""
        serializer = AccountActivationSerializer(data=request.data)
        
        if serializer.is_valid():
            uidb64 = serializer.validated_data['uidb64']
            token = serializer.validated_data['token']
            
            try:
                uid = force_str(urlsafe_base64_decode(uidb64))
                user = CustomUser.objects.get(pk=uid)
            except (TypeError, ValueError, OverflowError, CustomUser.DoesNotExist):
                return Response({
                    'success': False,
                    'message': 'Invalid activation link'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if account_activation_token.check_token(user, token):
                user.is_active = True
                user.save()
                
                return Response({
                    'success': True,
                    'message': 'Account activated successfully'
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': False,
                    'message': 'Invalid or expired activation link'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response({
            'success': False,
            'message': 'Invalid data',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
