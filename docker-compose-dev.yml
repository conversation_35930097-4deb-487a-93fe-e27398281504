version: '3.9'

services:
  app: &app
    build:
      context: .
      dockerfile: ./docker/develop/Dockerfile
    image: mogdynamics-image
    command: python manage.py runserver 0.0.0.0:8001
    env_file:
      - ./.env
    volumes:
      - ./:/mogdynamics-app-volume
    depends_on:
      - redis
    ports:
      - "8001:8001"
    restart: always

  redis:
    image: bitnami/redis:latest
    env_file:
      - ./.env
    ports:
      - "6380:6379"
    restart: always

  celery:
    <<: *app
    command: celery -A core worker -E --loglevel=INFO --concurrency=4
    ports: []
    volumes:
      - ./:/celery-volume
    env_file:
      - ./.env
    depends_on:
      - redis
      - app
    restart: always

  dashboard:
    <<: *app
    command: celery --broker=${REDIS_URL} flower --port=5555 --persistent=True --db=flower
    ports:
      - "5555:5555"
    env_file:
      - ./.env
    depends_on:
      - app
      - redis
      - celery
    # celery-beat:
    #   <<: *api
    #   command: celery -A core beat -l info
    #   ports: []
    #   volumes:
    #     - ./app:/app
    #   env_file:
    #     - ./.env
    #   depends_on:
    #     - redis
    #     - celery
    # volumes:
    #   mogdynamics-volume: ~
    #   redis-data-volume: ~
