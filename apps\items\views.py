from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from .models import ConsolidatedItem
from apps.exports.models import Item as ExportItem
from apps.imports.models import Item as ImportItem


def is_staff_user(user):
    """Check if user is staff"""
    return user.is_staff


@login_required
@user_passes_test(is_staff_user)
def consolidated_items_list(request):
    """Display all consolidated items with filtering and search"""
    items = ConsolidatedItem.objects.select_related(
        'customer', 'sender', 'destination', 'export', 'import_shipment'
    ).all()
    
    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        items = items.filter(
            Q(name__icontains=search_query) |
            Q(tracking_no__icontains=search_query) |
            Q(customer__first_name__icontains=search_query) |
            Q(customer__last_name__icontains=search_query) |
            Q(customer__email__icontains=search_query) |
            Q(payment_reference__icontains=search_query)
        )
    
    # Filter by item type
    item_type = request.GET.get('item_type', '')
    if item_type:
        items = items.filter(item_type=item_type)
    
    # Filter by shipping status
    shipping_status = request.GET.get('shipping_status', '')
    if shipping_status:
        items = items.filter(shipping_status=shipping_status)
    
    # Filter by payment status
    payment_status = request.GET.get('payment_status', '')
    if payment_status:
        items = items.filter(payment_status=payment_status)
    
    # Ordering
    order_by = request.GET.get('order_by', '-created_on')
    items = items.order_by(order_by)
    
    # Pagination
    paginator = Paginator(items, 25)  # Show 25 items per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistics
    stats = {
        'total_items': ConsolidatedItem.objects.count(),
        'export_items': ConsolidatedItem.objects.filter(item_type='export').count(),
        'import_items': ConsolidatedItem.objects.filter(item_type='import').count(),
        'pending_items': ConsolidatedItem.objects.filter(shipping_status='Pending').count(),
        'shipped_items': ConsolidatedItem.objects.filter(shipping_status='Shipped').count(),
        'unpaid_items': ConsolidatedItem.objects.filter(payment_status='Unpaid').count(),
        'paid_items': ConsolidatedItem.objects.filter(payment_status='Paid').count(),
    }
    
    # Get filter choices
    shipping_status_choices = ConsolidatedItem._meta.get_field('shipping_status').choices
    payment_status_choices = ConsolidatedItem._meta.get_field('payment_status').choices
    item_type_choices = ConsolidatedItem.ITEM_TYPE_CHOICES
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'current_item_type': item_type,
        'current_shipping_status': shipping_status,
        'current_payment_status': payment_status,
        'current_order_by': order_by,
        'stats': stats,
        'shipping_status_choices': shipping_status_choices,
        'payment_status_choices': payment_status_choices,
        'item_type_choices': item_type_choices,
    }
    
    return render(request, 'items/consolidated_items_list.html', context)


@login_required
@user_passes_test(is_staff_user)
def consolidated_item_detail(request, item_id):
    """Display detailed view of a consolidated item"""
    item = get_object_or_404(
        ConsolidatedItem.objects.select_related(
            'customer', 'sender', 'destination', 'export', 'import_shipment'
        ),
        id=item_id
    )
    
    context = {
        'item': item,
    }
    
    return render(request, 'items/consolidated_item_detail.html', context)


@login_required
@user_passes_test(is_staff_user)
@require_http_methods(["POST"])
def sync_items(request):
    """Sync all items from exports and imports via AJAX"""
    try:
        synced_count = 0
        
        # Sync export items
        for export_item in ExportItem.objects.select_related('export').all():
            ConsolidatedItem.sync_from_export_item(export_item)
            synced_count += 1
        
        # Sync import items
        for import_item in ImportItem.objects.select_related('imports').all():
            ConsolidatedItem.sync_from_import_item(import_item)
            synced_count += 1
        
        return JsonResponse({
            'success': True,
            'message': f'Successfully synced {synced_count} items from exports and imports.',
            'synced_count': synced_count
        })
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error syncing items: {str(e)}'
        })


@login_required
@user_passes_test(is_staff_user)
def dashboard_stats(request):
    """Get dashboard statistics for consolidated items"""
    stats = ConsolidatedItem.objects.aggregate(
        total_items=Count('id'),
        total_value=Sum('total_price'),
        total_weight=Sum('weight'),
    )
    
    # Additional stats by type
    export_stats = ConsolidatedItem.objects.filter(item_type='export').aggregate(
        count=Count('id'),
        value=Sum('total_price'),
        weight=Sum('weight'),
    )
    
    import_stats = ConsolidatedItem.objects.filter(item_type='import').aggregate(
        count=Count('id'),
        value=Sum('total_price'),
        weight=Sum('weight'),
    )
    
    # Status breakdown
    status_breakdown = {
        'shipping': {},
        'payment': {}
    }
    
    # Get shipping status breakdown
    shipping_statuses = ConsolidatedItem.objects.values('shipping_status').annotate(
        count=Count('id')
    ).order_by('shipping_status')
    
    for status in shipping_statuses:
        status_breakdown['shipping'][status['shipping_status']] = status['count']
    
    # Get payment status breakdown
    payment_statuses = ConsolidatedItem.objects.values('payment_status').annotate(
        count=Count('id')
    ).order_by('payment_status')
    
    for status in payment_statuses:
        status_breakdown['payment'][status['payment_status']] = status['count']
    
    context = {
        'stats': stats,
        'export_stats': export_stats,
        'import_stats': import_stats,
        'status_breakdown': status_breakdown,
    }
    
    return render(request, 'items/dashboard_stats.html', context)


@login_required
@user_passes_test(is_staff_user)
@require_http_methods(["POST"])
def bulk_update_status(request):
    """Bulk update shipping or payment status"""
    try:
        item_ids = request.POST.getlist('item_ids')
        update_type = request.POST.get('update_type')  # 'shipping' or 'payment'
        new_status = request.POST.get('new_status')
        
        if not item_ids or not update_type or not new_status:
            return JsonResponse({
                'success': False,
                'message': 'Missing required parameters'
            })
        
        items = ConsolidatedItem.objects.filter(id__in=item_ids)
        
        if update_type == 'shipping':
            updated_count = items.update(shipping_status=new_status)
            if new_status == 'Shipped':
                from django.utils import timezone
                items.update(shipped_at=timezone.now())
        elif update_type == 'payment':
            updated_count = items.update(payment_status=new_status)
        else:
            return JsonResponse({
                'success': False,
                'message': 'Invalid update type'
            })
        
        # Also update the original export/import objects
        for item in items:
            source_obj = item.source_object
            if source_obj:
                if update_type == 'shipping':
                    source_obj.shipping_status = new_status
                    if new_status == 'Shipped':
                        from django.utils import timezone
                        source_obj.shipped_at = timezone.now()
                elif update_type == 'payment':
                    source_obj.payment_status = new_status
                source_obj.save()
        
        return JsonResponse({
            'success': True,
            'message': f'Successfully updated {updated_count} items',
            'updated_count': updated_count
        })
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error updating items: {str(e)}'
        })
