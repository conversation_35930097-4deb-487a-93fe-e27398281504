{% extends 'base.html' %} {% load static %} {% block content %}
<!-- =-=-=-=-=-=-= PAGE BREADCRUMB =-=-=-=-=-=-= -->
<section class="breadcrumbs-area parallex">
  <div class="container">
    <div class="row">
      <div class="page-title">
        <div class="col-sm-12 col-md-6 page-heading text-left">
          <h3>Our pricing</h3>
          <h2>Pricing Page</h2>
        </div>
        <div class="col-sm-12 col-md-6 text-right">
          <ul class="breadcrumbs">
            <li><a href="{% url 'home' %}">home</a></li>
            <li><a href="{% url 'pricing' %}">pricing</a></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- =-=-=-=-=-=-= PAGE BREADCRUMB END =-=-=-=-=-=-= -->

<!-- =-=-=-=-=-=-= Pricing =-=-=-=-=-=-= -->
<section id="gallery" class="custom-padding">
  <div class="container">
    <div class="portfolio-container text-center">
      {% if price_list %}
      <ul id="portfolio-grid" class="three-column hover-two">
        {% for price in price_list %}
        <li class="portfolio-item gutter">
          <div class="portfolio">
            <div class="tt-overlay"></div>
            <img src="{{ price.image.url }}" alt="" />
          </div>
        </li>
        {% endfor %}
      </ul>
      {% else %}
      <h1>Pricing lists coming soon.</h1>
      {% endif %}
    </div>
    <!-- portfolio-container -->
  </div>
  <!-- end container -->
</section>
<!-- =-=-=-=-=-=-= Pricing end =-=-=-=-=-=-= -->
{% endblock content %}
