import requests
from core.settings import base
from django.views import generic
from .models import Logistic, Item
from django.contrib import messages
from apps.company.models import Company
from django.core.paginator import Paginator
from apps.logistics.forms import LogisticUpdateForm
from apps.logistics.paystack import initiate_payment
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404, render, redirect
from django.db import transaction


# Local order(logistics) tracking page
class TrackLocalOrderDeliveryView(generic.TemplateView):
    template_name = "logistics/logitisic-tracking-page.html"


# Local order(logistics) tracking result
class LocalOrderDeliveryResultView(generic.TemplateView):
    """
    Returns logistic order object from the search query.
    """
    model = Logistic
    template_name = "logistics/logistic-result-page.html"

    def get(self, request):
        tracking_no = self.request.GET.get("tracking-no", "")
        if tracking_no != "":
            result_obj = self.model.objects.filter(tracking_no=tracking_no).first()
            if result_obj:
                return render(request, self.template_name, {"result_obj": result_obj})
            messages.error(request, "No matching record found")
            return render(request, self.template_name, {"result_obj": result_obj})
        messages.error(request, "You cannot submit an empty query")
        return redirect("track_local_order")


@login_required(login_url='user_login')
def dispatcher_assigned_delivery_list(request):
    """
    Returns all local order deliveries assigned to dispatch riders.
    """
    template_name = "logistics/order-delivery-list.html"
    delivery_list = Logistic.objects.select_related('company', 'dispatcher').filter(dispatcher=request.user)
    context = {"delivery_list": delivery_list}
    return render(request, template_name, context)


@login_required(login_url='user_login')
def dispatcher_update_order_delivery(request, id):
    """
    Enables dispatch riders to update the delivery status of 
    local order  assigned to them.
    """
    form = LogisticUpdateForm(request.POST)
    order_obj = get_object_or_404(Logistic, id=id)
    template_name = 'logistics/dispatcher-update-order.html'
    if request.method == 'POST':
        form = LogisticUpdateForm(request.POST, instance=order_obj)
        if form.is_valid():
            form.save()
            messages.success(request, f"Order updated successfully")
            return redirect("local_order_delivery_update", id)
        messages.warning(request, f"Unable to update order")
        return redirect("update_profile", id)
    else:
        form = LogisticUpdateForm(instance=order_obj)
    context = {"form": form, "order_obj": order_obj}
    return render(request, template_name, context)


@login_required(login_url='user_login')
def company_order_list(request):
    """
    Returns lists of orders associated to a small business user.
    """
    template_name = "logistics/small-business-order-list.html"
    company_obj = get_object_or_404(Company, user_id=request.user.id)
    orders_list = Logistic.objects.select_related('company', 'sender').filter(company=company_obj)

    paginator = Paginator(orders_list, 10)
    page = request.GET.get('page')
    orders = paginator.get_page(page)

    context = {"orders": orders, "company_obj": company_obj}
    return render(request, template_name, context)


order_obj = ""


@login_required(login_url='user_login')
def company_order_detail(request, id):
    """
    Returns invidual orders details associated to a small business user.
    """

    template_name = "logistics/small-business-order-detail.html"
    company_obj = get_object_or_404(Company, user_id=request.user.id)
    order = Logistic.objects.select_related('company', 'sender').get(id=id)

    global order_obj
    order_obj = order

    items_list = Item.objects.filter(logistics=order).order_by("created_on")
    paginator = Paginator(items_list, 5)
    page = request.GET.get('page')
    items = paginator.get_page(page)

    context = {"order": order, "company_obj": company_obj, "items": items}
    return render(request, template_name, context)


# initial payment
@login_required(login_url="user_login")
@transaction.atomic
def payment_view(request):
    response = initiate_payment(request, order_obj)
    return response


# verify payment status
@login_required(login_url="user_login")
@transaction.atomic
def verify_payment(request):
    transaction_reference = request.GET.get("reference", None)

    paystack_live_secret_key = str(base.PAYSTACK_LIVE_SECRET_KEY)
    # paystack_test_secret_key = str(base.PAYSTACK_TEST_SECRET_KEY)
    headers = {"Authorization": f"Bearer {paystack_live_secret_key}"}
    url = f"{base.PAYSTACK_BASE_URL}verify/{transaction_reference}"
    response = requests.get(url, headers=headers, timeout=30)
    response_data = response.json()['data']
    order_fee = float(order_obj.delivery_fee * 100)

    if response_data['status'] == 'success' and response_data['amound'] == order_fee:
        Logistic.objects.select_for_update().filter(id=order_obj.id, payment_reference=transaction_reference).update(payment_status='paid')
        messages.success(request, f"payment transaction {response_data['status']}")
        return redirect("company_order_detail", order_obj.id)

    if response_data['status'] == 'pending':
        Logistic.objects.select_for_update().filter(id=order_obj.id, payment_reference=transaction_reference).update(payment_status='paid')
        messages.info(request, f"payment transaction is {response_data['status']}")
        return redirect("company_order_detail", order_obj.id)

    if response_data['status'] == 'failed':
        Logistic.objects.select_for_update().filter(id=order_obj.id, payment_reference=transaction_reference).update(payment_status='paid')
        messages.error(request, f"payment transaction has {response_data['status']}")
        return redirect("company_order_detail", order_obj.id)

    messages.error(request, f"{response_data['status']} : the payment transaction has failed.")
    return redirect("company_order_detail", order_obj.id)
