{% extends "base.html" %}
{% load humanize %}

{% block title %}{{ item.name }} - Item Details{% endblock %}

{% block extra_css %}
<style>
    .detail-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        padding: 30px;
        margin-bottom: 20px;
    }
    
    .item-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .item-type-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: bold;
        display: inline-block;
        margin-bottom: 10px;
    }
    
    .export-badge {
        background-color: rgba(40, 167, 69, 0.2);
        color: #28a745;
        border: 2px solid #28a745;
    }
    
    .import-badge {
        background-color: rgba(0, 123, 255, 0.2);
        color: #007bff;
        border: 2px solid #007bff;
    }
    
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .info-item {
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }
    
    .info-label {
        font-weight: bold;
        color: #495057;
        font-size: 0.9rem;
        margin-bottom: 5px;
    }
    
    .info-value {
        color: #212529;
        font-size: 1.1rem;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: bold;
    }
    
    .section-title {
        color: #495057;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Navigation -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'items:consolidated_items_list' %}">Consolidated Items</a>
                    </li>
                    <li class="breadcrumb-item active">{{ item.name }}</li>
                </ol>
            </nav>

            <!-- Item Header -->
            <div class="item-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <span class="item-type-badge {% if item.item_type == 'export' %}export-badge{% else %}import-badge{% endif %}">
                            {{ item.get_item_type_display }}
                        </span>
                        <h1 class="mb-2">{{ item.name }}</h1>
                        <p class="mb-0 opacity-75">
                            <i class="fas fa-barcode me-2"></i>Tracking: {{ item.tracking_no }}
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="mb-2">
                            <span class="status-badge bg-{% if item.shipping_status == 'Shipped' %}success{% elif item.shipping_status == 'Pending' %}warning text-dark{% else %}secondary{% endif %}">
                                <i class="fas fa-shipping-fast me-1"></i>{{ item.shipping_status }}
                            </span>
                        </div>
                        <div>
                            <span class="status-badge bg-{% if item.payment_status == 'Paid' %}success{% else %}danger{% endif %}">
                                <i class="fas fa-credit-card me-1"></i>{{ item.payment_status }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Item Information -->
                <div class="col-lg-6">
                    <div class="detail-card">
                        <h3 class="section-title">
                            <i class="fas fa-box me-2"></i>Item Information
                        </h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">Item Name</div>
                                <div class="info-value">{{ item.name }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Quantity</div>
                                <div class="info-value">{{ item.quantity }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Unit Price</div>
                                <div class="info-value">${{ item.price|floatformat:2 }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Total Price</div>
                                <div class="info-value"><strong>${{ item.total_price|floatformat:2 }}</strong></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Weight</div>
                                <div class="info-value">{{ item.weight }} kg</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Created On</div>
                                <div class="info-value">{{ item.created_on|date:"F d, Y g:i A" }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer & Shipping Information -->
                <div class="col-lg-6">
                    <div class="detail-card">
                        <h3 class="section-title">
                            <i class="fas fa-user me-2"></i>Customer & Shipping
                        </h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">Customer</div>
                                <div class="info-value">{{ item.customer_full_name }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Email</div>
                                <div class="info-value">{{ item.customer.email }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Phone Number</div>
                                <div class="info-value">{{ item.phone_no|default:"Not provided" }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Tracking Number</div>
                                <div class="info-value"><code>{{ item.tracking_no }}</code></div>
                            </div>
                            {% if item.sender %}
                            <div class="info-item">
                                <div class="info-label">Sender Address</div>
                                <div class="info-value">{{ item.sender }}</div>
                            </div>
                            {% endif %}
                            {% if item.destination %}
                            <div class="info-item">
                                <div class="info-label">Destination Address</div>
                                <div class="info-value">{{ item.destination }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Payment Information -->
                <div class="col-lg-6">
                    <div class="detail-card">
                        <h3 class="section-title">
                            <i class="fas fa-credit-card me-2"></i>Payment Information
                        </h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">Payment Status</div>
                                <div class="info-value">
                                    <span class="badge bg-{% if item.payment_status == 'Paid' %}success{% else %}danger{% endif %}">
                                        {{ item.payment_status }}
                                    </span>
                                </div>
                            </div>
                            {% if item.payment_reference %}
                            <div class="info-item">
                                <div class="info-label">Payment Reference</div>
                                <div class="info-value"><code>{{ item.payment_reference }}</code></div>
                            </div>
                            {% endif %}
                            <div class="info-item">
                                <div class="info-label">Shipping Fee</div>
                                <div class="info-value">
                                    {% if item.shipping_fee %}
                                        ${{ item.shipping_fee|floatformat:2 }}
                                    {% else %}
                                        Not set
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shipment Details -->
                <div class="col-lg-6">
                    <div class="detail-card">
                        <h3 class="section-title">
                            <i class="fas fa-shipping-fast me-2"></i>Shipment Details
                        </h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">Shipping Status</div>
                                <div class="info-value">
                                    <span class="badge bg-{% if item.shipping_status == 'Shipped' %}success{% elif item.shipping_status == 'Pending' %}warning text-dark{% else %}secondary{% endif %}">
                                        {{ item.shipping_status }}
                                    </span>
                                </div>
                            </div>
                            {% if item.shipped_at %}
                            <div class="info-item">
                                <div class="info-label">Shipped At</div>
                                <div class="info-value">{{ item.shipped_at|date:"F d, Y g:i A" }}</div>
                            </div>
                            {% endif %}
                            <div class="info-item">
                                <div class="info-label">Shipment Weight</div>
                                <div class="info-value">{{ item.get_shipment_weight }}</div>
                            </div>
                            {% if item.length and item.breadth and item.height %}
                            <div class="info-item">
                                <div class="info-label">Dimensions (L×W×H)</div>
                                <div class="info-value">{{ item.length }} × {{ item.breadth }} × {{ item.height }} cm</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Volumetric Weight</div>
                                <div class="info-value">{{ item.get_volumetric_weight }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="detail-card">
                        <h3 class="section-title">
                            <i class="fas fa-cogs me-2"></i>Actions
                        </h3>
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="{{ item.get_absolute_url }}" class="btn btn-primary" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>
                                View Original {{ item.get_item_type_display }}
                            </a>
                            <a href="{% url 'items:consolidated_items_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                Back to Items List
                            </a>
                            {% if item.item_type == 'export' and item.export %}
                                <a href="/admin/exports/export/{{ item.export.id }}/change/" class="btn btn-outline-info" target="_blank">
                                    <i class="fas fa-edit me-1"></i>
                                    Edit Export
                                </a>
                            {% elif item.item_type == 'import' and item.import_shipment %}
                                <a href="/admin/imports/import/{{ item.import_shipment.id }}/change/" class="btn btn-outline-info" target="_blank">
                                    <i class="fas fa-edit me-1"></i>
                                    Edit Import
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}