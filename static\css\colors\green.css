a , a:hover, a:focus , .header-area .logo-bar .info-box .icon, .slider-grids .tt-slider-title, .icons , .choose-box:hover .iconbox i, .choose-box:hover h4 , .news-detail h2:hover, .news-detail h2 a:hover , .footer-area .contact-info li:hover, footer-area .contact-info li a:hover , .footer-content .links-widget li:hover, .footer-content .links-widget li a:hover , .footer-content .news-widget .news-post a:hover,.portfolio-info .links a, .portfolio-details a, .portfolio-details a i, .slider-caption h1,.latest-news h4:hover, .latest-news h4 a:hover, .side-bar .widget ul li a:hover, .comment li a, .comment li a:hover, .choose-title:hover h2, .footer-content .column h2:hover, .footer-content .column h2 a:hover , .header-area .logo-bar .info-box div.text:hover, .navigation .navbar-default .navbar-nav > li > a:hover , .services-box-2 i, .error-text, .services-text:hover h4, .services-text:hover h4 a , .services-box-2:hover h4, .services-box-2:hover h4 a  {
	color: #5fc87a !important;
}
.navigation .dropdown-menu>li>a:hover, .navigation .dropdown-menu>li>a:focus, .navigation-2 .dropdown-menu>li>a:hover, .navigation-2 .dropdown-menu>li>a:focus ,.navigation-2 .navbar-default .navbar-nav>li>a:focus, .navigation-2 .navbar-default .navbar-nav>li>a:hover, .navigation-2 .navbar-default .navbar-nav>.open>a, .navigation-2 .navbar-default .navbar-nav>.open>a:focus, .navigation-2 .navbar-default .navbar-nav>.open>a:hover , .navigation-2 .navbar-default .navbar-toggle .icon-bar, #main-navigation .navbar-nav li.active , .top-bar .social-icons li a:hover{
	color: #fff !important;
	background-color: #5fc87a !important;
}
.navigation .navbar-default .navbar-nav > .open > a, .navigation .navbar-default .navbar-nav > .open > a:focus, .navigation .navbar-default .navbar-nav > .open > a:hover {
    background-color: transparent;
    color: #5fc87a !important;
}
.top-bar .nav-right > li > a:hover {
    color:#5fc87a !important;
 }
.nav-right  .dropdown-menu li a:hover, .nav-right  .dropdown-menu li a:focus{
     background-color: #5fc87a !important;
}
 .btn-colored {
    background-color: #5fc87a !important;
 }
  .btn-colored:hover {
     background-color: #4ca061 !important;
    border-color: #4ca061 !important;
	color:#fff !important;
 }
.profile-tabs .nav-tabs > .active > a, .profile-tabs .nav-tabs > .active > a:hover, .profile-tabs .nav-tabs > .active > a:focus {
    background: #5fc87a  none repeat scroll 0 0 !important;
}  
.profile-tabs .nav-tabs {
    border-color: -moz-use-text-color -moz-use-text-color #5fc87a !important;
}
.profile-tabs .nav-tabs > li > a:hover {
    background: #5fc87a none repeat scroll 0 0 !important;
}
.profile-usermenu ul li.active a {
    border-left: 2px solid #5fc87a !important;
}
.btn-primary {
    background-color: #5fc87a !important;
    border-color: #5fc87a !important;
	color:#fff !important;
}
.btn-primary:hover {
    background-color: #4ca061 !important;
    border-color: #4ca061 !important;
    color: #fff !important;
}
.process-icon {
    box-shadow: 0 0 0 2px #5fc87a !important;
 }
 .process-icon i {
    color: #5fc87a !important;
 }
 .our-app .btn:hover {
     background-color: #5fc87a !important;
     border:1px solid #5fc87a !important;
  }
 .our-process ul li:hover .process-icon {
     background:#5fc87a !important;
}
 #banner.hero-3 .form button {
    background: #5fc87a none repeat scroll 0 0 !important;
 }
 #banner.hero-3 .form button:hover {
    background: #4ca061 none repeat scroll 0 0 !important;
 }
.top-bar.color-scheme .social-icons li a:hover {
    background:none !important; 
}
.parallex-small, .quote .quotation-box , .quotation-box-1 {
    background: #5fc87a none repeat scroll 0 0 !important;
}
.services-grid:hover, .services-grid::before, clients-grid:hover {
    border: 2px solid #5fc87a !important;
}
blockquote {
    border-left: 5px solid #5fc87a !important;
}
.parallex::before {
    background: rgba(95, 200, 122, 0.9) !important;
}
.date > span , #clients .owl-prev, #clients .owl-next, #testimonials .owl-next, #testimonials .owl-prev, #services .owl-next, #services .owl-prev , #post-slider .owl-prev, #post-slider .owl-next {
    background-color: #5fc87a !important;
}
.news-detail h2, .news-detail h2 a , .slider-caption .btn:hover{
    color: #323232 !important;	
}

#clients .owl-prev:hover, #clients .owl-next:hover, #testimonials .owl-next:hover, #testimonials .owl-prev:hover, #services .owl-next:hover, #services .owl-prev:hover, #post-slider .owl-prev:hover, #post-slider .owl-next:hover {
    background-color: #4ca061 !important;
}
.clients-grid:hover {
    border: 2px solid #5fc87a !important;
}
.social-links-two a:hover {
    background: #5fc87a none repeat scroll 0 0 !important;
    border-color: #5fc87a !important;
    color: #fff !important;
}
.top-bar.color-scheme {
    background: #5fc87a none repeat scroll 0 0 !important;
    border-bottom: 1px solid #4ca061 !important;
}
.services-box-2 i {
    border: 3px solid #5fc87a !important;
}
.team-content {
    background: #5fc87a none repeat !important;
}
.accordion-box .accordion .accord-btn.active {
    background: #5fc87a none repeat scroll 0 0 !important;
    border-color: #5fc87a !important;
    color: #ffffff !important;
}
ul.side-bar-list li a:hover, ul.side-bar-list li a.active {
    background: #5fc87a none repeat scroll 0 0 !important;
}
.pagination > .active > a:hover, .pagination li:hover > a, .pagination > .active > a {
    background-color: #5fc87a !important;
    border-color: #5fc87a !important;
	color:#fff !important;
}
.side-bar .widget h2::before {
    background-color: #5fc87a !important;
}
.side-bar .widget .tag_cloud a:hover, .post-bottom .tag_cloud a:hover {
	color: #fff !important;
	background: #5fc87a repeat !important;
	border: 1px solid #5fc87a !important;
}
.post-bottom .social-icons a:hover {
	color: #fff !important;
	background: #5fc87a repeat !important;
}
.custom-heading h2::before {
    background-color: #5fc87a  !important;
}
.main-top .scroll-down::before, .main-top .scroll-down::after {
    border-bottom: 2px solid #5fc87a !important;
    border-right: 2px solid #5fc87a !important;
}
.main-top span#js-rotating {
	color:#5fc87a !important;
}