{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Consolidated Admin Dashboard{% endblock %}

{% block content %}
<div class="dashboard-container">
    <h1>Consolidated Import/Export Admin Dashboard</h1>
    
    <div class="dashboard-stats">
        <div class="stat-card">
            <h3>Total Shipments</h3>
            <p class="stat-number">{{ total_shipments }}</p>
        </div>
        
        <div class="stat-card">
            <h3>Total Imports</h3>
            <p class="stat-number">{{ total_imports }}</p>
        </div>
        
        <div class="stat-card">
            <h3>Total Exports</h3>
            <p class="stat-number">{{ total_exports }}</p>
        </div>
        
        <div class="stat-card">
            <h3>Total Items</h3>
            <p class="stat-number">{{ total_items }}</p>
        </div>
        
        <div class="stat-card">
            <h3>Total Revenue</h3>
            <p class="stat-number">${{ total_revenue|floatformat:2 }}</p>
        </div>
    </div>
    
    <div class="dashboard-charts">
        <div class="chart-container">
            <h3>Shipping Status Breakdown</h3>
            <table class="status-table">
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Count</th>
                    </tr>
                </thead>
                <tbody>
                    {% for status in status_breakdown %}
                    <tr>
                        <td>{{ status.shipping_status }}</td>
                        <td>{{ status.count }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="chart-container">
            <h3>Payment Status Breakdown</h3>
            <table class="status-table">
                <thead>
                    <tr>
                        <th>Payment Status</th>
                        <th>Count</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payment in payment_breakdown %}
                    <tr>
                        <td>{{ payment.payment_status }}</td>
                        <td>{{ payment.count }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="recent-shipments">
        <h3>Recent Shipments</h3>
        <table class="shipments-table">
            <thead>
                <tr>
                    <th>Tracking No</th>
                    <th>Type</th>
                    <th>Customer</th>
                    <th>Status</th>
                    <th>Payment</th>
                    <th>Created</th>
                </tr>
            </thead>
            <tbody>
                {% for shipment in recent_shipments %}
                <tr>
                    <td>
                        <a href="{% url 'consolidated_shipment_detail' shipment.pk %}">
                            {{ shipment.tracking_no }}
                        </a>
                    </td>
                    <td>
                        <span class="badge badge-{{ shipment.shipment_type }}">
                            {{ shipment.get_shipment_type_display }}
                        </span>
                    </td>
                    <td>{{ shipment.customer.first_name }} {{ shipment.customer.last_name }}</td>
                    <td>
                        <span class="badge badge-status-{{ shipment.shipping_status|lower }}">
                            {{ shipment.shipping_status }}
                        </span>
                    </td>
                    <td>
                        <span class="badge badge-payment-{{ shipment.payment_status|lower }}">
                            {{ shipment.payment_status }}
                        </span>
                    </td>
                    <td>{{ shipment.created_on|date:"M d, Y" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <div class="quick-actions">
        <h3>Quick Actions</h3>
        <div class="action-buttons">
            <a href="{% url 'consolidated_shipment_list' %}" class="btn btn-primary">
                View All Shipments
            </a>
            <a href="{% url 'consolidated_item_list' %}" class="btn btn-secondary">
                View All Items
            </a>
            <a href="{% url 'sync_data' %}" class="btn btn-warning">
                Sync Data
            </a>
            <a href="/admin/consolidated_admin/consolidatedshipment/" class="btn btn-success">
                Admin Interface
            </a>
        </div>
    </div>
</div>

<style>
.dashboard-container {
    padding: 20px;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #dee2e6;
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #007bff;
    margin: 10px 0;
}

.dashboard-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.status-table, .shipments-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.status-table th, .status-table td,
.shipments-table th, .shipments-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.status-table th, .shipments-table th {
    background-color: #e9ecef;
    font-weight: bold;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875em;
    font-weight: bold;
    color: white;
}

.badge-import { background-color: #007bff; }
.badge-export { background-color: #28a745; }
.badge-status-pending { background-color: #ffc107; color: #212529; }
.badge-status-processing { background-color: #17a2b8; }
.badge-status-shipped { background-color: #28a745; }
.badge-status-delivered { background-color: #155724; }
.badge-status-cancelled { background-color: #dc3545; }
.badge-payment-paid { background-color: #28a745; }
.badge-payment-unpaid { background-color: #dc3545; }

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    display: inline-block;
}

.btn-primary { background-color: #007bff; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }
.btn-warning { background-color: #ffc107; color: #212529; }
.btn-success { background-color: #28a745; color: white; }

.btn:hover {
    opacity: 0.8;
}
</style>
{% endblock %}