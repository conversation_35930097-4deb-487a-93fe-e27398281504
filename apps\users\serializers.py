from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import CustomUser


class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration"""
    password = serializers.CharField(
        write_only=True,
        min_length=8,
        validators=[validate_password]
    )
    confirm_password = serializers.CharField(write_only=True)
    
    class Meta:
        model = CustomUser
        fields = [
            'first_name', 'last_name', 'email', 'password', 'confirm_password',
            'gender', 'phone_no', 'address', 'city', 'state', 'country',
            'document_type', 'document', 'profile_picture'
        ]
        extra_kwargs = {
            'email': {'required': True},
            'first_name': {'required': True},
            'last_name': {'required': True},
        }

    def validate(self, attrs):
        """Validate password confirmation and email uniqueness"""
        if attrs['password'] != attrs['confirm_password']:
            raise serializers.ValidationError("Passwords do not match!")
        
        if CustomUser.objects.filter(email=attrs['email']).exists():
            raise serializers.ValidationError("Email already exists.")
        
        return attrs

    def create(self, validated_data):
        """Create user with validated data"""
        # Remove confirm_password from validated_data
        validated_data.pop('confirm_password', None)
        
        # Create user
        user = CustomUser.objects.create_user(
            email=validated_data['email'],
            password=validated_data['password'],
            first_name=validated_data['first_name'],
            last_name=validated_data['last_name'],
            gender=validated_data.get('gender', 'male'),
            phone_no=validated_data.get('phone_no', ''),
            address=validated_data.get('address', ''),
            city=validated_data.get('city', ''),
            state=validated_data.get('state', ''),
            country=validated_data.get('country', ''),
            document_type=validated_data.get('document_type', ''),
            document=validated_data.get('document', None),
            profile_picture=validated_data.get('profile_picture', None),
        )
        
        # Set user as inactive until email verification
        user.is_active = False
        user.user_type = "regular"
        user.save()
        
        return user


class UserLoginSerializer(serializers.Serializer):
    """Serializer for user login"""
    email = serializers.EmailField(required=True)
    password = serializers.CharField(required=True, write_only=True)

    def validate(self, attrs):
        """Validate user credentials"""
        email = attrs.get('email')
        password = attrs.get('password')

        if email and password:
            # Check if user exists
            if not CustomUser.objects.filter(email=email).exists():
                raise serializers.ValidationError("This email doesn't exist on our record.")
            
            # Get user object
            user = CustomUser.objects.filter(email=email).first()
            
            # Check if user is active
            if not user.is_active:
                raise serializers.ValidationError(
                    "Your email has not been activated. Check your email for activation link."
                )
            
            # Authenticate user
            user = authenticate(username=email, password=password)
            if not user:
                raise serializers.ValidationError("Invalid email and/or password combination.")
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError("Must include email and password.")


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile"""
    
    class Meta:
        model = CustomUser
        fields = [
            'id', 'profile_code', 'first_name', 'last_name', 'email',
            'gender', 'phone_no', 'address', 'city', 'state', 'country',
            'document_type', 'document', 'profile_picture', 'user_type',
            'created_on', 'updated_on'
        ]
        read_only_fields = ['id', 'profile_code', 'email', 'user_type', 'created_on', 'updated_on']


class UserUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user profile"""
    
    class Meta:
        model = CustomUser
        fields = [
            'first_name', 'last_name', 'phone_no', 'address', 
            'city', 'state', 'profile_picture'
        ]

    def update(self, instance, validated_data):
        """Update user profile"""
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class AccountActivationSerializer(serializers.Serializer):
    """Serializer for account activation"""
    uidb64 = serializers.CharField(required=True)
    token = serializers.CharField(required=True)
