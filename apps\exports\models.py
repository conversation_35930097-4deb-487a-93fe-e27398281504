from apps.addresses.models import Address
from django.db import models
from django.urls import reverse
from apps.common import generator, choices
from djmoney.models.fields import MoneyField
from apps.common.models import TimeStampedModel
from phonenumber_field.modelfields import PhoneNumberField
from apps.users.models import CustomUser


class Export(TimeStampedModel):
    customer = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    sender = models.ForeignKey(Address, on_delete=models.SET_NULL, related_name="sender", null=True)
    destination = models.ForeignKey(Address, on_delete=models.SET_NULL, null=True)
    phone_no = PhoneNumberField(blank=True, max_length=14)
    weight = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    weight_unit = models.CharField(choices=choices.weight_unit, default="Kg")
    lenght = models.FloatField(default=0.00, blank=True)
    breadth = models.FloatField(default=0.00, blank=True)
    height = models.FloatField(default=0.00, blank=True)
    volumentary_weight = models.FloatField(default=0.00, blank=True)
    volumentary_weight_unit = models.CharField(choices=choices.weight_unit, default="Kg")
    export_fee = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    tracking_no = models.CharField(default=generator.export_tracking_no, max_length=50, db_index=True)
    payment_status = models.CharField(choices=choices.shipping_payment_choices, default="Unpaid", max_length=100)
    payment_reference = models.CharField(max_length=100, blank=True, null=True)
    shipped_at = models.DateTimeField(null=True, blank=True)
    tracking_url = models.URLField(blank=True)
    shipping_status = models.CharField(default="Pending", max_length=255, choices=choices.shipping_status_choices)
    attention = models.TextField(blank=True)
    email_sent = models.BooleanField(default=False)

    class Meta:
        verbose_name = "Export"
        verbose_name_plural = "Exports"
        ordering = ('-created_on',)

    def __str__(self):
        return f"{self.customer} {self.tracking_no}"

    def get_absolute_url(self):
        return reverse("export_detail", kwargs={"id": self.id})

    # def get_receiver_address(self):
    #     return f"{self.receiver_address.capitalize()}, {self.receiver_city.capitalize()}, {self.receiver_state.capitalize()}, {self.receiver_country.capitalize()}"

    @property
    def get_weight(self):
        return f"{self.weight} {self.weight_unit}"

    @property
    def get_volumentary_weight(self):
        return f"{self.volumentary_weight} {self.volumentary_weight_unit.capitalize()}"

    def save(self, *args, **kwargs):
        self.volumentary_weight = round((self.lenght * self.breadth * self.height)/5000, 2)
        return super().save(*args, **kwargs)


class Item(TimeStampedModel):
    export = models.ForeignKey(Export, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=255)
    quantity = models.IntegerField(default=1)
    price = MoneyField(max_digits=10, decimal_places=2, default_currency='USD')
    total_price = MoneyField(max_digits=10, decimal_places=2, default_currency='USD', blank=True, null=True)
    weight = models.FloatField()

    class Meta:
        verbose_name = "Item"
        verbose_name_plural = "Items"

    def __str__(self):
        return f"{self.export} {self.name}"

    def get_absolute_url(self):
        return reverse("item_detail", kwargs={"id": self.id})

    def save(self, *args, **kwargs):
        total_price = self.price * self.quantity
        self.total_price = total_price
        return super().save(*args, **kwargs)
