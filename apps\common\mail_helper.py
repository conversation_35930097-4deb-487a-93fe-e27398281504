from django.core.mail import EmailMessage, send_mail


# Maling class.
class SendEmailClass():
    def __init__(self, email_subject, email_body, sender_email, receiver_email, reply_to: list):
        self.email_subject = email_subject
        self.email_body = email_body
        self.sender_email = sender_email
        self.receiver_email = receiver_email
        self.reply_to = reply_to

    def account_activation_mail(self):
        msg = EmailMessage(
            subject=self.email_subject,
            body=self.email_body,
            from_email=self.sender_email,
            to=[str(self.receiver_email)],
            reply_to=None
        )
        msg.send(fail_silently=True)
        return True
    
    def account_activation_status_mail(self):
        msg = EmailMessage(
            subject=self.email_subject,
            body=self.email_body,
            from_email=self.sender_email,
            to=[str(self.receiver_email)],
            reply_to=None
        )
        msg.send(fail_silently=True)
        return True

    def password_reset_mail(self):
        msg = EmailMessage(
            subject=self.email_subject,
            body=self.email_body,
            from_email=self.sender_email,
            to=[str(self.receiver_email)],
            reply_to=None
        )
        msg.send(fail_silently=True)
        return True

    def contact_us_mail(self):
        msg = EmailMessage(
            subject=self.email_subject,
            body=self.email_body,
            from_email=self.sender_email,
            to=[str(self.receiver_email)],
            reply_to=[self.reply_to]
        )
        msg.send(fail_silently=True)
        return "Done"

    def order_booking_mail(self):
        msg = EmailMessage(
            subject=self.email_subject,
            body=self.email_body,
            from_email=self.sender_email,
            to=[str(self.receiver_email)],
            reply_to=[self.reply_to]
        )
        msg.send(fail_silently=True)
        return "Done"

    def export_mail(self):
        msg = EmailMessage(
            subject=self.email_subject,
            body=self.email_body,
            from_email=self.sender_email,
            to=[str(self.receiver_email)],
            reply_to=[self.reply_to]
        )
        msg.send(fail_silently=True)
        return True

    def import_mail(self):
        msg = EmailMessage(
            subject=self.email_subject,
            body=self.email_body,
            from_email=self.sender_email,
            to=[str(self.receiver_email)],
            reply_to=[self.reply_to]
        )
        msg.send(fail_silently=True)
        return True

    def local_logistics_mail(self):
        send_mail(
            subject=self.email_subject,
            message=self.email_body,
            from_email=self.sender_email,
            recipient_list=[self.receiver_email,],
            fail_silently=False,
        )
        return True
