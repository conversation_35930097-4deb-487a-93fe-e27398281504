/* postgresql Database Configuration */

ENGINE=django.db.backends.postgres_psycopg2',
NAME="dbname"
USER="db username"
PASSWORD="db password"
HOST="db host"
PORT="db port"



SECRET_KEY="django secret key"
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_USE_TLS=True
EMAIL_PORT=587
EMAIL_HOST_USER="your email"
EMAIL_HOST_PASSWORD="your password"
DEFAULT_FROM_EMAIL=Test<<EMAIL>
