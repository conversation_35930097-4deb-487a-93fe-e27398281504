from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db import transaction
from .models import Import, Item
from .serializers import (
    ImportCreateSerializer,
    ImportListSerializer, 
    ImportDetailSerializer,
    ImportTrackingSerializer
)


class ImportCreateAPIView(generics.CreateAPIView):
    """API View to create new import shipment"""
    serializer_class = ImportCreateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """Create new import shipment"""
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            import_shipment = serializer.save()
            
            # Return created shipment data
            detail_serializer = ImportDetailSerializer(import_shipment)
            
            return Response({
                'success': True,
                'message': 'Import shipment created successfully',
                'data': {
                    'id': str(import_shipment.id),
                    'tracking_no': import_shipment.tracking_no,
                    'import_fee': import_shipment.import_fee,
                    'payment_status': import_shipment.payment_status,
                    'shipping_status': import_shipment.shipping_status
                }
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'message': 'Import shipment creation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class ImportListAPIView(generics.ListAPIView):
    """API View to list user's import shipments"""
    serializer_class = ImportListSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get imports for current user"""
        return Import.objects.select_related('customer', 'sender', 'destination').filter(
            customer=self.request.user
        ).order_by('-created_on')
    
    def list(self, request, *args, **kwargs):
        """List user's import shipments with pagination"""
        queryset = self.get_queryset()
        
        # Get pagination parameters
        page = request.GET.get('page', 1)
        limit = min(int(request.GET.get('limit', 20)), 100)  # Max 100 items per page
        
        # Paginate results
        paginator = Paginator(queryset, limit)
        page_obj = paginator.get_page(page)
        
        # Serialize data
        serializer = self.get_serializer(page_obj.object_list, many=True)
        
        return Response({
            'success': True,
            'data': {
                'count': paginator.count,
                'next': page_obj.next_page_number() if page_obj.has_next() else None,
                'previous': page_obj.previous_page_number() if page_obj.has_previous() else None,
                'results': serializer.data
            }
        }, status=status.HTTP_200_OK)


class ImportDetailAPIView(generics.RetrieveAPIView):
    """API View to get detailed import shipment information"""
    serializer_class = ImportDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'
    
    def get_queryset(self):
        """Get imports for current user"""
        return Import.objects.select_related('customer', 'sender', 'destination').filter(
            customer=self.request.user
        )
    
    def retrieve(self, request, *args, **kwargs):
        """Get detailed import shipment information"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class ImportTrackingAPIView(APIView):
    """API View for public import tracking"""
    permission_classes = [permissions.AllowAny]  # Public endpoint
    
    def get(self, request, tracking_number):
        """Track import shipment by tracking number"""
        try:
            import_shipment = Import.objects.select_related('sender', 'destination').get(
                tracking_no=tracking_number
            )
            
            serializer = ImportTrackingSerializer(import_shipment)
            
            return Response({
                'success': True,
                'data': {
                    'tracking_no': import_shipment.tracking_no,
                    'type': 'import',
                    'status': import_shipment.shipping_status,
                    'sender': serializer.data['sender'],
                    'destination': serializer.data['destination'],
                    'weight': serializer.data['weight_display'],
                    'shipped_at': import_shipment.shipped_at,
                    'estimated_delivery': None,  # You can add logic for estimated delivery
                    'tracking_history': [
                        {
                            'status': 'Shipment Created',
                            'timestamp': import_shipment.created_on,
                            'location': serializer.data['sender']['address'] if serializer.data['sender'] else 'Origin'
                        },
                        {
                            'status': import_shipment.shipping_status,
                            'timestamp': import_shipment.shipped_at or import_shipment.updated_on,
                            'location': 'In Transit' if import_shipment.shipping_status != 'Pending' else 'Origin'
                        }
                    ]
                }
            }, status=status.HTTP_200_OK)
            
        except Import.DoesNotExist:
            return Response({
                'success': False,
                'message': 'No matching import shipment found for this tracking number'
            }, status=status.HTTP_404_NOT_FOUND)


class ImportStatsAPIView(APIView):
    """API View to get import statistics for user"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get user's import statistics"""
        user = request.user
        
        # Get import statistics
        total_imports = Import.objects.filter(customer=user).count()
        pending_imports = Import.objects.filter(customer=user, shipping_status='Pending').count()
        in_transit_imports = Import.objects.filter(customer=user, shipping_status='In Transit').count()
        delivered_imports = Import.objects.filter(customer=user, shipping_status='Delivered').count()
        unpaid_imports = Import.objects.filter(customer=user, payment_status='Unpaid').count()
        
        return Response({
            'success': True,
            'data': {
                'total_imports': total_imports,
                'pending_imports': pending_imports,
                'in_transit_imports': in_transit_imports,
                'delivered_imports': delivered_imports,
                'unpaid_imports': unpaid_imports
            }
        }, status=status.HTTP_200_OK)
