{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Sync Data - Consolidated Admin{% endblock %}

{% block content %}
<div class="sync-container">
    <h1>Sync Import/Export Data</h1>
    
    {% if messages %}
        <div class="messages">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}
    
    <div class="sync-stats">
        <div class="stat-card">
            <h3>Total Imports</h3>
            <p class="stat-number">{{ import_count }}</p>
            <p class="stat-label">in imports app</p>
        </div>
        
        <div class="stat-card">
            <h3>Total Exports</h3>
            <p class="stat-number">{{ export_count }}</p>
            <p class="stat-label">in exports app</p>
        </div>
        
        <div class="stat-card">
            <h3>Consolidated Records</h3>
            <p class="stat-number">{{ consolidated_count }}</p>
            <p class="stat-label">in consolidated admin</p>
        </div>
        
        <div class="stat-card">
            <h3>Unsynced Imports</h3>
            <p class="stat-number text-warning">{{ unsynced_imports }}</p>
            <p class="stat-label">need syncing</p>
        </div>
        
        <div class="stat-card">
            <h3>Unsynced Exports</h3>
            <p class="stat-number text-warning">{{ unsynced_exports }}</p>
            <p class="stat-label">need syncing</p>
        </div>
    </div>
    
    <div class="sync-actions">
        <h3>Sync Actions</h3>
        <p>Use the buttons below to sync data from the imports and exports apps into the consolidated admin interface.</p>
        
        <form method="post" class="sync-form">
            {% csrf_token %}
            
            <div class="sync-options">
                <div class="sync-option">
                    <button type="submit" name="sync_type" value="imports" class="btn btn-primary">
                        <i class="icon-import"></i>
                        Sync Imports Only
                        {% if unsynced_imports > 0 %}
                            <span class="badge">{{ unsynced_imports }}</span>
                        {% endif %}
                    </button>
                    <p class="option-description">
                        Sync all import records and their items from the imports app.
                    </p>
                </div>
                
                <div class="sync-option">
                    <button type="submit" name="sync_type" value="exports" class="btn btn-success">
                        <i class="icon-export"></i>
                        Sync Exports Only
                        {% if unsynced_exports > 0 %}
                            <span class="badge">{{ unsynced_exports }}</span>
                        {% endif %}
                    </button>
                    <p class="option-description">
                        Sync all export records and their items from the exports app.
                    </p>
                </div>
                
                <div class="sync-option">
                    <button type="submit" name="sync_type" value="both" class="btn btn-warning">
                        <i class="icon-sync"></i>
                        Sync Both
                        {% if unsynced_imports > 0 or unsynced_exports > 0 %}
                            <span class="badge">{{ unsynced_imports|add:unsynced_exports }}</span>
                        {% endif %}
                    </button>
                    <p class="option-description">
                        Sync all import and export records in one operation.
                    </p>
                </div>
            </div>
        </form>
    </div>
    
    <div class="sync-info">
        <h3>Important Information</h3>
        <div class="info-box">
            <h4>What happens during sync?</h4>
            <ul>
                <li>Creates consolidated shipment records for each import/export</li>
                <li>Creates consolidated item records for each item in the shipments</li>
                <li>Maintains references to original records for data integrity</li>
                <li>Skips records that have already been synced</li>
                <li>Preserves all original data and relationships</li>
            </ul>
        </div>
        
        <div class="info-box">
            <h4>Data Safety</h4>
            <ul>
                <li>Original import/export data is never modified</li>
                <li>Sync operation is safe to run multiple times</li>
                <li>Only new records are created during sync</li>
                <li>No data is deleted during the sync process</li>
            </ul>
        </div>
    </div>
    
    <div class="navigation-links">
        <a href="{% url 'consolidated_dashboard' %}" class="btn btn-secondary">
            ← Back to Dashboard
        </a>
        <a href="{% url 'consolidated_shipment_list' %}" class="btn btn-info">
            View Consolidated Shipments
        </a>
        <a href="/admin/consolidated_admin/" class="btn btn-dark">
            Admin Interface
        </a>
    </div>
</div>

<style>
.sync-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.messages {
    margin-bottom: 20px;
}

.alert {
    padding: 12px 20px;
    border-radius: 4px;
    margin-bottom: 10px;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.sync-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #dee2e6;
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #007bff;
    margin: 10px 0;
}

.stat-number.text-warning {
    color: #ffc107;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9em;
}

.sync-actions {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 8px;
    margin-bottom: 30px;
    border: 1px solid #dee2e6;
}

.sync-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.sync-option {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.sync-option button {
    width: 100%;
    margin-bottom: 10px;
    position: relative;
}

.option-description {
    color: #6c757d;
    font-size: 0.9em;
    margin: 0;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    font-weight: bold;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    transition: opacity 0.2s;
}

.btn:hover {
    opacity: 0.8;
}

.btn-primary { background-color: #007bff; color: white; }
.btn-success { background-color: #28a745; color: white; }
.btn-warning { background-color: #ffc107; color: #212529; }
.btn-secondary { background-color: #6c757d; color: white; }
.btn-info { background-color: #17a2b8; color: white; }
.btn-dark { background-color: #343a40; color: white; }

.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 4px 8px;
    font-size: 0.75em;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sync-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.info-box {
    background: #e9ecef;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.info-box h4 {
    margin-top: 0;
    color: #495057;
}

.info-box ul {
    margin-bottom: 0;
}

.info-box li {
    margin-bottom: 5px;
}

.navigation-links {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .sync-info {
        grid-template-columns: 1fr;
    }
    
    .sync-options {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}