/* @font-face {
  font-family: 'egfont';
  src: url('../fonts/egfont.eot?62883676');
  src: url('../fonts/egfont.eot?62883676#iefix') format('embedded-opentype'),
     url('../fonts/egfont.woff?62883676') format('woff'),
       url('../fonts/egfont.ttf?62883676') format('truetype'),
       url('../fonts/egfont.svg?62883676#egfont') format('svg');
  font-weight: normal;
  font-style: normal;
} */

/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'egfont';
    src: url('../font/egfont.svg?62883676#egfont') format('svg');
  }
}
*/
 
 [class^="icon-"]:before, [class*=" icon-"]:before {
  font-family: "egfont";
  font-style: normal;
  font-weight: normal;
  /* speak: none; */
 
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */
 
  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;
     
  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;
 
  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;
 
  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */
 
  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}
 
.icon-music:before { content: '\e858'; } /* '' */
.icon-color-adjust:before { content: '\e862'; } /* '' */
.icon-mail:before { content: '\e868'; } /* '' */
.icon-mail-alt:before { content: '\e869'; } /* '' */
.icon-heart:before { content: '\e86a'; } /* '' */
.icon-heart-empty:before { content: '\e86b'; } /* '' */
.icon-star:before { content: '\e86c'; } /* '' */
.icon-star-empty:before { content: '\e86d'; } /* '' */
.icon-user:before { content: '\e879'; } /* '' */
.icon-male:before { content: '\e87a'; } /* '' */
.icon-female:before { content: '\e87b'; } /* '' */
.icon-video:before { content: '\e805'; } /* '' */
.icon-videocam:before { content: '\e859'; } /* '' */
.icon-picture-1:before { content: '\e803'; } /* '' */
.icon-camera:before { content: '\e85a'; } /* '' */
.icon-camera-alt:before { content: '\e85b'; } /* '' */
.icon-th-large:before { content: '\e83c'; } /* '' */
.icon-th:before { content: '\e83d'; } /* '' */
.icon-ok:before { content: '\e80d'; } /* '' */
.icon-ok-circled2:before { content: '\e877'; } /* '' */
.icon-ok-squared:before { content: '\e878'; } /* '' */
.icon-cancel:before { content: '\e80a'; } /* '' */
.icon-plus:before { content: '\e86e'; } /* '' */
.icon-plus-circled:before { content: '\e876'; } /* '' */
.icon-plus-squared:before { content: '\e875'; } /* '' */
.icon-minus:before { content: '\e86f'; } /* '' */
.icon-minus-circled:before { content: '\e870'; } /* '' */
.icon-minus-squared:before { content: '\e871'; } /* '' */
.icon-minus-squared-alt:before { content: '\e872'; } /* '' */
.icon-info-circled:before { content: '\e80f'; } /* '' */
.icon-info:before { content: '\e844'; } /* '' */
.icon-home:before { content: '\e843'; } /* '' */
.icon-link:before { content: '\e80e'; } /* '' */
.icon-unlink:before { content: '\e83f'; } /* '' */
.icon-link-ext:before { content: '\e840'; } /* '' */
.icon-lock:before { content: '\e856'; } /* '' */
.icon-lock-open:before { content: '\e857'; } /* '' */
.icon-eye:before { content: '\e841'; } /* '' */
.icon-eye-off:before { content: '\e842'; } /* '' */
.icon-tag:before { content: '\e85c'; } /* '' */
.icon-thumbs-up:before { content: '\e887'; } /* '' */
.icon-thumbs-up-alt:before { content: '\e888'; } /* '' */
.icon-download:before { content: '\e88a'; } /* '' */
.icon-upload:before { content: '\e889'; } /* '' */
.icon-reply:before { content: '\e8a6'; } /* '' */
.icon-forward:before { content: '\e874'; } /* '' */
.icon-export-1:before { content: '\e873'; } /* '' */
.icon-print:before { content: '\e886'; } /* '' */
.icon-gamepad:before { content: '\e837'; } /* '' */
.icon-trash:before { content: '\e801'; } /* '' */
.icon-doc-text:before { content: '\e885'; } /* '' */
.icon-doc-inv:before { content: '\e884'; } /* '' */
.icon-folder-1:before { content: '\e83e'; } /* '' */
.icon-folder-open:before { content: '\e883'; } /* '' */
.icon-folder-open-empty:before { content: '\e882'; } /* '' */
.icon-rss:before { content: '\e880'; } /* '' */
.icon-rss-squared:before { content: '\e881'; } /* '' */
.icon-phone:before { content: '\e87f'; } /* '' */
.icon-menu:before { content: '\e830'; } /* '' */
.icon-cog-alt:before { content: '\e847'; } /* '' */
.icon-wrench:before { content: '\e848'; } /* '' */
.icon-basket-1:before { content: '\e87c'; } /* '' */
.icon-calendar:before { content: '\e87d'; } /* '' */
.icon-calendar-empty:before { content: '\e87e'; } /* '' */
.icon-lightbulb:before { content: '\e88b'; } /* '' */
.icon-resize-full-alt:before { content: '\e845'; } /* '' */
.icon-move:before { content: '\e846'; } /* '' */
.icon-down-dir:before { content: '\e838'; } /* '' */
.icon-up-dir:before { content: '\e839'; } /* '' */
.icon-left-dir:before { content: '\e817'; } /* '' */
.icon-right-dir:before { content: '\e818'; } /* '' */
.icon-down-open:before { content: '\e83b'; } /* '' */
.icon-left-open:before { content: '\e819'; } /* '' */
.icon-right-open:before { content: '\e81a'; } /* '' */
.icon-angle-left:before { content: '\e820'; } /* '' */
.icon-angle-right:before { content: '\e81d'; } /* '' */
.icon-angle-double-left:before { content: '\e892'; } /* '' */
.icon-angle-double-right:before { content: '\e893'; } /* '' */
.icon-left-big:before { content: '\e81f'; } /* '' */
.icon-right-big:before { content: '\e81e'; } /* '' */
.icon-up-hand:before { content: '\e835'; } /* '' */
.icon-ccw-1:before { content: '\e891'; } /* '' */
.icon-shuffle-1:before { content: '\e849'; } /* '' */
.icon-play:before { content: '\e88c'; } /* '' */
.icon-play-circled:before { content: '\e88e'; } /* '' */
.icon-stop:before { content: '\e88f'; } /* '' */
.icon-pause:before { content: '\e88d'; } /* '' */
.icon-fast-fw:before { content: '\e890'; } /* '' */
.icon-desktop:before { content: '\e85d'; } /* '' */
.icon-laptop:before { content: '\e85e'; } /* '' */
.icon-tablet:before { content: '\e85f'; } /* '' */
.icon-mobile:before { content: '\e860'; } /* '' */
.icon-flight:before { content: '\e894'; } /* '' */
.icon-font:before { content: '\e851'; } /* '' */
.icon-bold:before { content: '\e852'; } /* '' */
.icon-italic:before { content: '\e855'; } /* '' */
.icon-text-height:before { content: '\e84f'; } /* '' */
.icon-text-width:before { content: '\e850'; } /* '' */
.icon-align-left:before { content: '\e84c'; } /* '' */
.icon-align-center:before { content: '\e84d'; } /* '' */
.icon-align-right:before { content: '\e84e'; } /* '' */
.icon-search:before { content: '\e802'; } /* '' */
.icon-indent-left:before { content: '\e866'; } /* '' */
.icon-indent-right:before { content: '\e867'; } /* '' */
.icon-ajust:before { content: '\e84a'; } /* '' */
.icon-tint:before { content: '\e84b'; } /* '' */
.icon-chart-bar:before { content: '\e853'; } /* '' */
.icon-magic:before { content: '\e807'; } /* '' */
.icon-sort:before { content: '\e895'; } /* '' */
.icon-sort-alt-up:before { content: '\e863'; } /* '' */
.icon-sort-alt-down:before { content: '\e864'; } /* '' */
.icon-sort-name-up:before { content: '\e854'; } /* '' */
.icon-sort-name-down:before { content: '\e865'; } /* '' */
.icon-coffee:before { content: '\e896'; } /* '' */
.icon-food:before { content: '\e897'; } /* '' */
.icon-medkit:before { content: '\e898'; } /* '' */
.icon-puzzle:before { content: '\e899'; } /* '' */
.icon-apple:before { content: '\e89a'; } /* '' */
.icon-facebook:before { content: '\e89b'; } /* '' */
.icon-gplus:before { content: '\e89c'; } /* '' */
.icon-tumblr:before { content: '\e8a0'; } /* '' */
.icon-twitter-squared:before { content: '\e8a2'; } /* '' */
.icon-twitter:before { content: '\e8a1'; } /* '' */
.icon-vimeo-squared:before { content: '\e89d'; } /* '' */
.icon-youtube:before { content: '\e89f'; } /* '' */
.icon-youtube-squared:before { content: '\e89e'; } /* '' */
.icon-picture:before { content: '\e800'; } /* '' */
.icon-check:before { content: '\e810'; } /* '' */
.icon-back:before { content: '\e8a5'; } /* '' */
.icon-thumbs-up-1:before { content: '\e8a9'; } /* '' */
.icon-thumbs-down:before { content: '\e8aa'; } /* '' */
.icon-download-1:before { content: '\e8ab'; } /* '' */
.icon-upload-1:before { content: '\e8ac'; } /* '' */
.icon-reply-1:before { content: '\e8a8'; } /* '' */
.icon-forward-1:before { content: '\e8a7'; } /* '' */
.icon-export:before { content: '\e80b'; } /* '' */
.icon-folder:before { content: '\e813'; } /* '' */
.icon-rss-1:before { content: '\e8c8'; } /* '' */
.icon-cog:before { content: '\e832'; } /* '' */
.icon-tools:before { content: '\e815'; } /* '' */
.icon-basket:before { content: '\e812'; } /* '' */
.icon-login:before { content: '\e833'; } /* '' */
.icon-logout:before { content: '\e834'; } /* '' */
.icon-resize-full:before { content: '\e816'; } /* '' */
.icon-popup:before { content: '\e828'; } /* '' */
.icon-arrow-combo:before { content: '\e827'; } /* '' */
.icon-left-open-1:before { content: '\e82a'; } /* '' */
.icon-right-open-1:before { content: '\e82b'; } /* '' */
.icon-left-open-mini:before { content: '\e822'; } /* '' */
.icon-right-open-mini:before { content: '\e823'; } /* '' */
.icon-left-open-big:before { content: '\e824'; } /* '' */
.icon-right-open-big:before { content: '\e825'; } /* '' */
.icon-left:before { content: '\e836'; } /* '' */
.icon-right:before { content: '\e826'; } /* '' */
.icon-ccw:before { content: '\e808'; } /* '' */
.icon-cw:before { content: '\e8c9'; } /* '' */
.icon-arrows-ccw:before { content: '\e806'; } /* '' */
.icon-level-down:before { content: '\e8a3'; } /* '' */
.icon-level-up:before { content: '\e8a4'; } /* '' */
.icon-shuffle:before { content: '\e814'; } /* '' */
.icon-palette:before { content: '\e829'; } /* '' */
.icon-list-add:before { content: '\e80c'; } /* '' */
.icon-back-in-time:before { content: '\e821'; } /* '' */
.icon-monitor:before { content: '\e81b'; } /* '' */
.icon-paper-plane:before { content: '\e8ad'; } /* '' */
.icon-brush:before { content: '\e8ae'; } /* '' */
.icon-droplet:before { content: '\e81c'; } /* '' */
.icon-clipboard:before { content: '\e8b0'; } /* '' */
.icon-megaphone:before { content: '\e8b1'; } /* '' */
.icon-key:before { content: '\e8af'; } /* '' */
.icon-github:before { content: '\e8b3'; } /* '' */
.icon-github-circled:before { content: '\e8b4'; } /* '' */
.icon-flickr:before { content: '\e8b2'; } /* '' */
.icon-flickr-circled:before { content: '\e8b5'; } /* '' */
.icon-vimeo:before { content: '\e8b6'; } /* '' */
.icon-vimeo-circled:before { content: '\e8b7'; } /* '' */
.icon-twitter-1:before { content: '\e8b8'; } /* '' */
.icon-twitter-circled:before { content: '\e8b9'; } /* '' */
.icon-facebook-1:before { content: '\e8ba'; } /* '' */
.icon-facebook-circled:before { content: '\e8bb'; } /* '' */
.icon-facebook-squared:before { content: '\e8bc'; } /* '' */
.icon-gplus-1:before { content: '\e8bd'; } /* '' */
.icon-gplus-circled:before { content: '\e8be'; } /* '' */
.icon-pinterest:before { content: '\e8bf'; } /* '' */
.icon-pinterest-circled:before { content: '\e8c0'; } /* '' */
.icon-tumblr-1:before { content: '\e8c1'; } /* '' */
.icon-tumblr-circled:before { content: '\e8c2'; } /* '' */
.icon-linkedin:before { content: '\e8c3'; } /* '' */
.icon-linkedin-circled:before { content: '\e8c4'; } /* '' */
.icon-dribbble:before { content: '\e8c5'; } /* '' */
.icon-dribbble-circled:before { content: '\e8c6'; } /* '' */
.icon-picasa:before { content: '\e8c7'; } /* '' */
.icon-soundcloud:before { content: '\e8ca'; } /* '' */
.icon-ok-1:before { content: '\e811'; } /* '' */
.icon-doc:before { content: '\e809'; } /* '' */
.icon-left-open-outline:before { content: '\e82e'; } /* '' */
.icon-left-open-2:before { content: '\e82c'; } /* '' */
.icon-right-open-outline:before { content: '\e82f'; } /* '' */
.icon-right-open-2:before { content: '\e82d'; } /* '' */
.icon-equalizer:before { content: '\e83a'; } /* '' */
.icon-layers-alt:before { content: '\e804'; } /* '' */
.icon-pencil-1:before { content: '\e831'; } /* '' */
.icon-align-justify:before { content: '\e861'; } /* '' */