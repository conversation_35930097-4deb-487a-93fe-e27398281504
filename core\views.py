from core import tasks
from core.settings import base
from django.contrib import messages
from apps.company.models import Company
from django.shortcuts import render, redirect
from django.template.loader import render_to_string
from django.contrib.auth.decorators import login_required
from apps.galleries.models import Gallery


# Returns site's home page.
def home_view(request):
    gallery_list = Gallery.objects.all()
    template_name = "index.html"
    context = {"gallery_list": gallery_list}
    return render(request, template_name, context)


# Return about page.
def about_view(request):
    template_name = "about.html"
    return render(request, template_name)


# Returns services page.
def service_view(request):
    template_name = "services.html"
    return render(request, template_name)


# Returns our team page.
def our_team_view(request):
    template_name = "team.html"
    return render(request, template_name)


# Returns cargo booking page. Login is required!.
@login_required(login_url="user_login")
def cargo_booking_view(request):
    template_name = "online_booking.html"
    if request.method == 'POST':
        departure_location = request.POST.get("departure-location", ""),
        delivery_location = request.POST.get("delivery-location", ""),
        if departure_location == delivery_location:
            messages.info(request, f'Departure location cannot be the same as delivery location.')
            return redirect("cargo_booking")
        context = {
            "name": request.user.get_full_name(),
            "phone_no": request.user.phone_no,
            "shipping_mode": request.POST.get("shipping-mode", ""),
            "commodity_name": request.POST.get("commodity-name", ""),
            "total_estimated_weight": request.POST.get("total-estimated-weight", ""),
            "weight_unit": request.POST.get("weight-unit", ""),
            "packaging": request.POST.get("packaging", ""),
            "quantity": request.POST.get("quantity", ""),
            "type_of_cargo": request.POST.get("type-of-cargo", ""),
            "insurance_required": request.POST.get("insurance-required", ""),
            "departure_location": departure_location,
            "delivery_location": delivery_location,
            "pick_up_date": request.POST.get("pick-up-date", ""),
            "additional_detail": request.POST.get("additional-detail", "")
        }
        message = render_to_string('online_booking_data.html', context)
        subject = f"New booking from {context['name']}"
        tasks.send_order_booking_email_task.delay(
            subject=subject,
            message=message,
            from_email=base.DEFAULT_FROM_EMAIL,
            receiver_email=base.CONTACT_EMAIL,
            reply_to=request.user.email
        )
        messages.success(request, "Booking successful.")
        return redirect("home")
    return render(request, template_name)


# Returns our processes page.
def process_view(request):
    template_name = "process.html"
    return render(request, template_name)


# Returns contact page.
def contact_view(request):
    template_name = "contact.html"
    if request.method == "POST":
        client_email = request.POST.get("email", "")
        subject = request.POST.get("subject", "")
        name = request.POST.get("name", "")
        message = request.POST.get("message", "")
        tasks.send_contact_mail_task.delay(
            subject=f"Mail from {name}: {subject}",
            message=message,
            receiver_email=base.DEFAULT_FROM_EMAIL,
            sender_email=base.CONTACT_EMAIL,
            reply_to=client_email
        )
        messages.success(request, f"Mail sent successfully.")
        return redirect('home')
    return render(request, template_name)


def base_template(request):
    template_name = "base.html"
    company_obj = Company.objects.select_related('user').filter(user=request.user)
    context = {"company_obj": company_obj}
    return render(request, template_name, context)


def dashboard_template(request):
    template_name = "dashboard.html"
    company_obj = Company.objects.select_related('user').filter(user=request.user).first()
    context = {"company_obj": company_obj}
    return render(request, template_name, context)


def custom_page_404_not_found(request, exception):
    template_name = "404.html"
    return render(request, template_name)


def custom_page_500_server_error(request, exception=None):
    template_name = "500.html"
    return render(request, template_name)
