from django.db import models
from django.urls import reverse
from apps.common import choices
from apps.users.models import CustomUser
from apps.addresses.models import Address
from djmoney.models.fields import MoneyField
from apps.common.models import TimeStampedModel
from phonenumber_field.modelfields import PhoneNumberField
from apps.exports.models import Export
from apps.imports.models import Import


class ConsolidatedItem(TimeStampedModel):
    """
    Consolidated model that holds all items from both exports and imports
    """
    ITEM_TYPE_CHOICES = [
        ('export', 'Export Item'),
        ('import', 'Import Item'),
    ]
    
    # Basic item information
    name = models.CharField(max_length=255)
    quantity = models.IntegerField(default=1)
    price = MoneyField(max_digits=10, decimal_places=2, default_currency='USD')
    total_price = MoneyField(max_digits=10, decimal_places=2, default_currency='USD', blank=True, null=True)
    weight = models.FloatField()
    
    # Type and source tracking
    item_type = models.CharField(max_length=10, choices=ITEM_TYPE_CHOICES)
    
    # Foreign keys to original items (only one will be populated)
    export = models.ForeignKey(Export, on_delete=models.CASCADE, null=True, blank=True)
    import_shipment = models.ForeignKey(Import, on_delete=models.CASCADE, null=True, blank=True)
    
    # Consolidated shipment information (denormalized for easy access)
    customer = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    sender = models.ForeignKey(
        Address, on_delete=models.SET_NULL,
        related_name="consolidated_item_sender", null=True, blank=True
    )
    destination = models.ForeignKey(
        Address, on_delete=models.SET_NULL,
        related_name="consolidated_item_destination", null=True, blank=True
    )
    phone_no = PhoneNumberField(blank=True, max_length=14)
    tracking_no = models.CharField(max_length=50, db_index=True)
    payment_status = models.CharField(choices=choices.shipping_payment_choices, default="Unpaid", max_length=100)
    payment_reference = models.CharField(max_length=100, blank=True, null=True)
    shipped_at = models.DateTimeField(null=True, blank=True)
    shipping_status = models.CharField(default="Pending", max_length=255, choices=choices.shipping_status_choices)
    
    # Shipment dimensions and weight
    shipment_weight = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    weight_unit = models.CharField(choices=choices.weight_unit, default="Kg")
    length = models.FloatField(default=0.00, blank=True)
    breadth = models.FloatField(default=0.00, blank=True)
    height = models.FloatField(default=0.00, blank=True)
    volumetric_weight = models.FloatField(default=0.00, blank=True)
    volumetric_weight_unit = models.CharField(choices=choices.weight_unit, default="Kg")
    
    # Fees
    shipping_fee = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    class Meta:
        verbose_name = "Consolidated Item"
        verbose_name_plural = "Consolidated Items"
        ordering = ('-created_on',)
        indexes = [
            models.Index(fields=['item_type']),
            models.Index(fields=['tracking_no']),
            models.Index(fields=['customer']),
            models.Index(fields=['shipping_status']),
            models.Index(fields=['payment_status']),
        ]

    def __str__(self):
        return f"{self.get_item_type_display()} - {self.name} ({self.tracking_no})"

    def get_absolute_url(self):
        if self.item_type == 'export':
            return reverse("export_detail", kwargs={"id": self.export.id})
        else:
            return reverse("import_detail", kwargs={"id": self.import_shipment.id})

    @property
    def get_shipment_weight(self):
        return f"{self.shipment_weight} {self.weight_unit}"

    @property
    def get_volumetric_weight(self):
        return f"{self.volumetric_weight} {self.volumetric_weight_unit.capitalize()}"

    @property
    def customer_full_name(self):
        return f"{self.customer.first_name} {self.customer.last_name}"

    @property
    def source_object(self):
        """Returns the original export or import object"""
        if self.item_type == 'export':
            return self.export
        else:
            return self.import_shipment

    def save(self, *args, **kwargs):
        # Calculate total price
        if self.price and self.quantity:
            self.total_price = self.price * self.quantity
        
        # Calculate volumetric weight if dimensions are provided
        if self.length and self.breadth and self.height:
            self.volumetric_weight = round((self.length * self.breadth * self.height) / 5000, 2)
        
        return super().save(*args, **kwargs)

    @classmethod
    def sync_from_export_item(cls, export_item):
        """Create or update consolidated item from export item"""
        export_obj = export_item.export
        
        consolidated_item, created = cls.objects.get_or_create(
            export=export_obj,
            name=export_item.name,
            defaults={
                'item_type': 'export',
                'quantity': export_item.quantity,
                'price': export_item.price,
                'total_price': export_item.total_price,
                'weight': export_item.weight,
                'customer': export_obj.customer,
                'sender': export_obj.sender,
                'destination': export_obj.destination,
                'phone_no': export_obj.phone_no,
                'tracking_no': export_obj.tracking_no,
                'payment_status': export_obj.payment_status,
                'payment_reference': export_obj.payment_reference,
                'shipped_at': export_obj.shipped_at,
                'shipping_status': export_obj.shipping_status,
                'shipment_weight': export_obj.weight,
                'weight_unit': export_obj.weight_unit,
                'length': export_obj.lenght,  # Note: keeping original typo from export model
                'breadth': export_obj.breadth,
                'height': export_obj.height,
                'volumetric_weight': export_obj.volumentary_weight,
                'volumetric_weight_unit': export_obj.volumentary_weight_unit,
                'shipping_fee': export_obj.export_fee,
            }
        )
        
        if not created:
            # Update existing record
            consolidated_item.quantity = export_item.quantity
            consolidated_item.price = export_item.price
            consolidated_item.total_price = export_item.total_price
            consolidated_item.weight = export_item.weight
            consolidated_item.customer = export_obj.customer
            consolidated_item.sender = export_obj.sender
            consolidated_item.destination = export_obj.destination
            consolidated_item.phone_no = export_obj.phone_no
            consolidated_item.tracking_no = export_obj.tracking_no
            consolidated_item.payment_status = export_obj.payment_status
            consolidated_item.payment_reference = export_obj.payment_reference
            consolidated_item.shipped_at = export_obj.shipped_at
            consolidated_item.shipping_status = export_obj.shipping_status
            consolidated_item.shipment_weight = export_obj.weight
            consolidated_item.weight_unit = export_obj.weight_unit
            consolidated_item.length = export_obj.lenght
            consolidated_item.breadth = export_obj.breadth
            consolidated_item.height = export_obj.height
            consolidated_item.volumetric_weight = export_obj.volumentary_weight
            consolidated_item.volumetric_weight_unit = export_obj.volumentary_weight_unit
            consolidated_item.shipping_fee = export_obj.export_fee
            consolidated_item.save()
        
        return consolidated_item

    @classmethod
    def sync_from_import_item(cls, import_item):
        """Create or update consolidated item from import item"""
        import_obj = import_item.imports
        
        consolidated_item, created = cls.objects.get_or_create(
            import_shipment=import_obj,
            name=import_item.name,
            defaults={
                'item_type': 'import',
                'quantity': import_item.quantity,
                'price': import_item.price,
                'total_price': import_item.total_price,
                'weight': import_item.weight,
                'customer': import_obj.customer,
                'sender': import_obj.sender,
                'destination': import_obj.destination,
                'phone_no': import_obj.phone_no,
                'tracking_no': import_obj.tracking_no,
                'payment_status': import_obj.payment_status,
                'payment_reference': import_obj.payment_reference,
                'shipped_at': import_obj.shipped_at,
                'shipping_status': import_obj.shipping_status,
                'shipment_weight': import_obj.weight,
                'weight_unit': import_obj.weight_unit,
                'length': import_obj.lenght,  # Note: keeping original typo from import model
                'breadth': import_obj.breadth,
                'height': import_obj.height,
                'volumetric_weight': import_obj.volumentary_weight,
                'volumetric_weight_unit': import_obj.volumentary_weight_unit,
                'shipping_fee': import_obj.import_fee,
            }
        )
        
        if not created:
            # Update existing record
            consolidated_item.quantity = import_item.quantity
            consolidated_item.price = import_item.price
            consolidated_item.total_price = import_item.total_price
            consolidated_item.weight = import_item.weight
            consolidated_item.customer = import_obj.customer
            consolidated_item.sender = import_obj.sender
            consolidated_item.destination = import_obj.destination
            consolidated_item.phone_no = import_obj.phone_no
            consolidated_item.tracking_no = import_obj.tracking_no
            consolidated_item.payment_status = import_obj.payment_status
            consolidated_item.payment_reference = import_obj.payment_reference
            consolidated_item.shipped_at = import_obj.shipped_at
            consolidated_item.shipping_status = import_obj.shipping_status
            consolidated_item.shipment_weight = import_obj.weight
            consolidated_item.weight_unit = import_obj.weight_unit
            consolidated_item.length = import_obj.lenght
            consolidated_item.breadth = import_obj.breadth
            consolidated_item.height = import_obj.height
            consolidated_item.volumetric_weight = import_obj.volumentary_weight
            consolidated_item.volumetric_weight_unit = import_obj.volumentary_weight_unit
            consolidated_item.shipping_fee = import_obj.import_fee
            consolidated_item.save()
        
        return consolidated_item
